[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "email-validator>=2.2.0",
    "flask-cors>=6.0.1",
    "flask>=3.1.1",
    "flask-sqlalchemy>=3.1.1",
    "google-genai>=1.25.0",
    "gunicorn>=23.0.0",
    "psycopg2-binary>=2.9.10",
    "requests>=2.32.4",
    "sqlalchemy>=2.0.41",
    "werkzeug>=3.1.3",
]
