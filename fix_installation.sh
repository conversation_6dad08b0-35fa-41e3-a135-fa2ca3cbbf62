#!/bin/bash

# Fix Installation Script for TasteShift
# Handles pandas compilation issues and PostgreSQL setup

echo "🔧 TasteShift Installation Fix Script"
echo "====================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Update pip first
print_status "Updating pip to latest version..."
pip install --upgrade pip
print_success "Pip updated"

# Step 2: Install build dependencies
print_status "Installing build dependencies..."
pip install wheel setuptools Cython

# Step 3: Install PostgreSQL dependencies first
print_status "Installing PostgreSQL adapter..."
pip install psycopg2-binary
if [ $? -eq 0 ]; then
    print_success "psycopg2-binary installed successfully"
else
    print_warning "psycopg2-binary failed, trying alternative..."
    # Try installing PostgreSQL first if not available
    if command -v brew &> /dev/null; then
        brew install postgresql libpq
        export PATH="/opt/homebrew/opt/libpq/bin:$PATH"
        export LDFLAGS="-L/opt/homebrew/opt/libpq/lib"
        export CPPFLAGS="-I/opt/homebrew/opt/libpq/include"
        pip install psycopg2-binary
    else
        print_error "Please install Homebrew first: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
fi

# Step 4: Install numpy first (pandas dependency)
print_status "Installing numpy..."
pip install "numpy>=1.24.0"

# Step 5: Install pandas with pre-compiled wheel
print_status "Installing pandas (using pre-compiled wheel)..."
pip install --only-binary=pandas "pandas>=2.0.0"
if [ $? -ne 0 ]; then
    print_warning "Pre-compiled pandas failed, trying without compilation..."
    pip install --no-build-isolation "pandas>=2.0.0"
fi

# Step 6: Install core Flask dependencies
print_status "Installing Flask and core dependencies..."
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==3.0.5
pip install Flask-CORS==4.0.0
pip install Werkzeug==2.3.7
pip install SQLAlchemy==2.0.21

# Step 7: Install API dependencies
print_status "Installing API dependencies..."
pip install requests==2.31.0
pip install urllib3==2.0.4
pip install "google-generativeai>=0.3.0"

# Step 8: Install utility dependencies
print_status "Installing utility dependencies..."
pip install python-dateutil==2.8.2
pip install python-dotenv==1.0.0
pip install colorama==0.4.6
pip install "cryptography>=41.0.0"
pip install jsonschema==4.19.1
pip install httpx==0.24.1

# Step 9: Install remaining dependencies
print_status "Installing remaining dependencies..."
pip install click==8.1.7
pip install itsdangerous==2.1.2
pip install Jinja2==3.1.2
pip install MarkupSafe==2.1.3

# Step 10: Test imports
print_status "Testing critical imports..."
python -c "import pandas; print('✅ pandas:', pandas.__version__)" 2>/dev/null
python -c "import numpy; print('✅ numpy:', numpy.__version__)" 2>/dev/null
python -c "import psycopg2; print('✅ psycopg2: OK')" 2>/dev/null
python -c "import flask; print('✅ flask:', flask.__version__)" 2>/dev/null
python -c "import google.generativeai; print('✅ google-generativeai: OK')" 2>/dev/null

echo ""
print_success "Installation completed!"
print_status "You can now run: bash start_server.sh"
echo ""
