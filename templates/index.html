<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TasteShift - AI-Powered Cultural Intelligence Platform | Hackathon Winner</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-2.26.0.min.js"></script>
    <!-- Spline 3D Viewer for Body Scan Animation -->
    <script type="module" src="https://unpkg.com/@splinetool/viewer@1.9.28/build/spline-viewer.js"></script>
</head>
<body>
    <div id="app">
        <!-- Enhanced Navigation -->
        <nav class="navbar navbar-expand-lg fixed-top">
            <div class="container">
                <a class="navbar-brand animate-slide-in" href="#" onclick="showHome()">
                    <i class="fas fa-brain me-2"></i>TasteShift
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <div class="navbar-nav ms-auto">
                        <a class="nav-link" href="#" onclick="showHome()">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                        <a class="nav-link" href="#" onclick="showInsights()">
                            <i class="fas fa-chart-bar me-1"></i>Insights
                        </a>
                        <a class="nav-link" href="#" onclick="showWinningFeatures()">
                            <i class="fas fa-trophy me-1"></i>Features
                        </a>
                        <a class="nav-link" href="#" onclick="showPersonas()">
                            <i class="fas fa-users me-1"></i>Personas
                        </a>
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-robot me-1"></i>AI Tools
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="showCulturalChat()">
                                    <i class="fas fa-comments me-2"></i>Cultural Chat
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="showCrossculturalAdapter()">
                                    <i class="fas fa-exchange-alt me-2"></i>Campaign Adapter
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="showTrendAnalyzer()">
                                    <i class="fas fa-chart-line me-2"></i>Trend Analyzer
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="showCulturalRiskAssessment()">
                                    <i class="fas fa-shield-alt me-2"></i>Risk Assessment
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="showBusinessValueCalculator()">
                                    <i class="fas fa-calculator me-2"></i>ROI Calculator
                                </a></li>
                            </ul>
                        </div>
                        <a class="nav-link" href="/onboarding">
                            <i class="fas fa-question-circle me-1"></i>Guide
                        </a>
                        <a class="nav-link" href="/api/documentation" target="_blank" style="color: #10b981; font-weight: 600;">
                            <i class="fas fa-book me-1"></i>API Docs
                        </a>
                    </div>
                </div>
            </div>
        </nav>



        <!-- Home Section -->
        <div id="home-section" class="container">
            <!-- Hero Section -->
            <div class="hero-section">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-6 mb-5">
                            <h1 class="display-4 mb-4 animate-fade-in animate-glow">
                                <i class="fas fa-brain text-gradient me-3 animate-pulse"></i>
                                TasteShift
                            </h1>
                            <h2 class="h3 text-gradient mb-4 animate-fade-in" style="animation-delay: 0.2s;">
                                AI-Powered Cultural Intelligence Platform
                            </h2>
                            <p class="lead animate-fade-in" style="animation-delay: 0.3s;">
                                🏆 <strong>Key Features:</strong> Voice Interface • Real-time Analytics • Business Intelligence • ROI Calculator
                            </p>
                            <p class="lead animate-fade-in" style="animation-delay: 0.4s;">
                                Generate authentic audience personas and analyze campaign alignment using
                                AI-powered cultural insights from real-time global data.
                            </p>
                            <div class="d-flex flex-wrap gap-3 animate-fade-in" style="animation-delay: 0.5s;">
                                <div class="badge animate-float">
                                    <i class="fas fa-database me-2"></i>Real Cultural Data
                                </div>
                                <div class="badge animate-float" style="animation-delay: 0.5s;">
                                    <i class="fas fa-brain me-2"></i>AI-Powered Analysis
                                </div>
                                <div class="badge animate-float" style="animation-delay: 1s;">
                                    <i class="fas fa-chart-line me-2"></i>Instant Insights
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="hero-image-placeholder animate-slide-in-right" style="animation-delay: 0.7s;">
                                <div class="text-center p-5">
                                    <i class="fas fa-brain fa-5x text-gradient mb-3 animate-pulse"></i>
                                    <h4 class="text-gradient">AI-Powered Intelligence</h4>
                                    <p class="text-muted">Real-time cultural insights at your fingertips</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Platform Statistics -->
            <div class="row justify-content-center mb-5">
                <div class="col-lg-10">
                    <div class="stats-cards animate-slide-in-up" style="animation-delay: 0.3s;">
                        <div class="row g-4">
                            <div class="col-md-3 col-6">
                                <div class="card text-center animate-float">
                                    <div class="card-body">
                                        <div class="feature-icon" style="width: 60px; height: 60px; font-size: 1.5rem; margin-bottom: 1rem;">
                                            <i class="fas fa-globe"></i>
                                        </div>
                                        <h4 class="status-success text-glow">195+</h4>
                                        <small class="text-muted">Countries</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-6">
                                <div class="card text-center animate-float" style="animation-delay: 0.2s;">
                                    <div class="card-body">
                                        <div class="feature-icon" style="width: 60px; height: 60px; font-size: 1.5rem; margin-bottom: 1rem;">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <h4 class="status-warning text-glow">&lt;30s</h4>
                                        <small class="text-muted">Generation Time</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-6">
                                <div class="card text-center animate-float" style="animation-delay: 0.4s;">
                                    <div class="card-body">
                                        <div class="feature-icon" style="width: 60px; height: 60px; font-size: 1.5rem; margin-bottom: 1rem;">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <h4 class="status-success text-glow">50+</h4>
                                        <small class="text-muted">Demographics</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-6">
                                <div class="card text-center animate-float" style="animation-delay: 0.6s;">
                                    <div class="card-body">
                                        <div class="feature-icon" style="width: 60px; height: 60px; font-size: 1.5rem; margin-bottom: 1rem;">
                                            <i class="fas fa-chart-bar"></i>
                                        </div>
                                        <h4 class="status-success text-glow">98%</h4>
                                        <small class="text-muted">Accuracy</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="text-center mb-5">
                        <h1 class="display-4 mb-4 text-gradient animate-fade-in">
                            <i class="fas fa-magic text-gradient me-3 animate-pulse"></i>
                            Start Creating
                        </h1>
                        <p class="lead animate-fade-in" style="animation-delay: 0.2s;">Generate audience personas and analyze campaign alignment using AI-powered cultural insights</p>
                    </div>

                    <!-- Step 1: Generate Persona -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3><i class="fas fa-user-plus me-2"></i>Step 1: Generate Cultural Persona</h3>
                        </div>
                        <div class="card-body">
                            <form id="persona-form">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="region" class="form-label">
                                            <i class="fas fa-globe me-2"></i>Region
                                        </label>
                                        <select class="form-select" id="region" required>
                                            <option value="">Select a region...</option>
                                            <option value="United States">🇺🇸 United States</option>
                                            <option value="United Kingdom">🇬🇧 United Kingdom</option>
                                            <option value="Canada">🇨🇦 Canada</option>
                                            <option value="Australia">🇦🇺 Australia</option>
                                            <option value="Germany">🇩🇪 Germany</option>
                                            <option value="France">🇫🇷 France</option>
                                            <option value="Italy">🇮🇹 Italy</option>
                                            <option value="Spain">🇪🇸 Spain</option>
                                            <option value="Netherlands">🇳🇱 Netherlands</option>
                                            <option value="Japan">🇯🇵 Japan</option>
                                            <option value="South Korea">🇰🇷 South Korea</option>
                                            <option value="Singapore">🇸🇬 Singapore</option>
                                            <option value="Hong Kong">🇭🇰 Hong Kong</option>
                                            <option value="India">🇮🇳 India</option>
                                            <option value="Brazil">🇧🇷 Brazil</option>
                                            <option value="Mexico">🇲🇽 Mexico</option>
                                            <option value="Argentina">🇦🇷 Argentina</option>
                                            <option value="Sweden">🇸🇪 Sweden</option>
                                            <option value="Denmark">🇩🇰 Denmark</option>
                                            <option value="Norway">🇳🇴 Norway</option>
                                            <option value="Seoul">🏙️ Seoul</option>
                                            <option value="Tokyo">🏙️ Tokyo</option>
                                            <option value="London">🏙️ London</option>
                                            <option value="New York">🏙️ New York</option>
                                            <option value="Berlin">🏙️ Berlin</option>
                                            <option value="Paris">🏙️ Paris</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="demographic" class="form-label">
                                            <i class="fas fa-users me-2"></i>Target Demographic
                                        </label>
                                        <select class="form-select" id="demographic" required>
                                            <option value="">Select a demographic...</option>
                                            <option value="Gen Z">🎮 Gen Z (Born 1997-2012)</option>
                                            <option value="Millennials">📱 Millennials (Born 1981-1996)</option>
                                            <option value="Gen X">💼 Gen X (Born 1965-1980)</option>
                                            <option value="Baby Boomers">📺 Baby Boomers (Born 1946-1964)</option>
                                            <option value="Urban Professionals">🏢 Urban Professionals</option>
                                            <option value="Creative Professionals">🎨 Creative Professionals</option>
                                            <option value="Students">📚 Students</option>
                                            <option value="Parents">👨‍👩‍👧‍👦 Parents</option>
                                            <option value="Tech Enthusiasts">💻 Tech Enthusiasts</option>
                                            <option value="Health & Wellness Focused">🏃‍♀️ Health & Wellness Focused</option>
                                            <option value="Luxury Consumers">💎 Luxury Consumers</option>
                                            <option value="Budget Conscious">💰 Budget Conscious</option>
                                            <option value="Eco-Conscious">🌱 Eco-Conscious</option>
                                            <option value="Entrepreneurs">🚀 Entrepreneurs</option>
                                            <option value="Retirees">🏖️ Retirees</option>
                                            <option value="Fashion Enthusiasts">👗 Fashion Enthusiasts</option>
                                            <option value="Music Lovers">🎵 Music Lovers</option>
                                            <option value="Foodies">🍴 Foodies</option>
                                            <option value="Travelers">✈️ Travelers</option>
                                            <option value="Fitness Enthusiasts">💪 Fitness Enthusiasts</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg animate-pulse" id="generate-btn">
                                        <i class="fas fa-magic me-2"></i>Generate Cultural Persona
                                    </button>
                                    <p class="mt-3 text-muted small">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Uses real cultural data from Qloo API + AI analysis from Google Gemini
                                    </p>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Enhanced Persona Results -->
                    <div id="persona-results" class="persona-results-container mb-4" style="display: none;">
                        <div class="persona-header-card card mb-3">
                            <div class="card-header persona-header">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="persona-title-section">
                                        <h3 class="persona-title mb-1">
                                            <i class="fas fa-user-circle me-2"></i>
                                            <span id="persona-title-text">Generated Persona</span>
                                        </h3>
                                        <div class="persona-meta" id="persona-meta">
                                            <!-- Dynamic meta info will be populated here -->
                                        </div>
                                    </div>
                                    <div class="persona-actions">
                                        <button class="btn btn-outline-primary btn-sm me-2" onclick="exportPersona()">
                                            <i class="fas fa-download me-1"></i>Export
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" onclick="sharePersona()">
                                            <i class="fas fa-share me-1"></i>Share
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Landscape Persona Layout -->
                        <div class="persona-landscape-container">
                            <!-- Top Section: Persona Content in Landscape Format -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="persona-main-content-landscape">
                                        <div id="persona-content" class="persona-content-area-landscape">
                                            <!-- Dynamic persona content will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>

                    <!-- Step 2: Campaign Analysis -->
                    <div id="campaign-section" class="card mb-4" style="display: none;">
                        <div class="card-header">
                            <h3><i class="fas fa-bullhorn me-2"></i>Step 2: Analyze Campaign</h3>
                        </div>
                        <div class="card-body">
                            <form id="campaign-form">
                                <div class="mb-3">
                                    <label for="campaign-brief" class="form-label">Campaign Brief</label>
                                    <textarea class="form-control" id="campaign-brief" rows="5" 
                                              placeholder="Paste your campaign brief here..." required></textarea>
                                </div>
                                <button type="submit" class="btn btn-success" id="analyze-btn">
                                    <i class="fas fa-chart-line me-2"></i>Analyze Campaign
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Analysis Results -->
                    <div id="analysis-results" class="card mb-4" style="display: none;">
                        <div class="card-header">
                            <h3><i class="fas fa-analytics me-2"></i>Campaign Analysis</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-8">
                                    <div id="analysis-content"></div>
                                </div>
                                <div class="col-lg-4">
                                    <!-- Taste Shock Score Gauge -->
                                    <div class="card border-gradient mb-3">
                                        <div class="card-header">
                                            <h5><i class="fas fa-tachometer-alt me-2"></i>Taste Shock Score</h5>
                                        </div>
                                        <div class="card-body text-center">
                                            <div style="height: 200px;">
                                                <canvas id="campaign-score-gauge"></canvas>
                                            </div>
                                            <div class="mt-3">
                                                <div id="score-interpretation" class="alert alert-info">
                                                    <small>Score interpretation will appear here</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Creative Suggestions Visual -->
                                    <div class="card border-gradient">
                                        <div class="card-header">
                                            <h5><i class="fas fa-lightbulb me-2"></i>Suggestions Overview</h5>
                                        </div>
                                        <div class="card-body">
                                            <div style="height: 200px;">
                                                <canvas id="suggestions-chart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cultural Intelligence Chat Section -->
        <div id="cultural-chat-section" class="container" style="display: none;">
            <div class="row">
                <div class="col-12 mb-4">
                    <h2 class="text-gradient animate-fade-in">
                        <i class="fas fa-robot me-3"></i>Cultural Intelligence Assistant
                    </h2>
                    <p class="lead text-muted">Ask questions about cultural preferences, marketing strategies, and consumer behavior</p>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card border-gradient">
                        <div class="card-header">
                            <h4><i class="fas fa-comments me-2"></i>Chat with Cultural AI</h4>
                        </div>
                        <div class="card-body">
                            <div id="chat-messages" class="chat-container mb-3" style="height: 400px; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 10px; padding: 15px;">
                                <div class="chat-message ai-message">
                                    <div class="message-content">
                                        <strong>Cultural AI:</strong> Hello! I'm your Cultural Intelligence Assistant. Ask me anything about cultural preferences, marketing strategies, consumer behavior, or cross-cultural adaptation. For example:
                                        <ul class="mt-2">
                                            <li>"Why do Gen Z consumers in Japan prefer minimalist aesthetics?"</li>
                                            <li>"How should I adapt my campaign for the Indian market?"</li>
                                            <li>"What cultural trends are emerging in Latin America?"</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="input-group">
                                <input type="text" id="chat-input" class="form-control" placeholder="Ask about cultural insights, trends, or marketing strategies..." onkeypress="handleChatKeyPress(event)">
                                <button class="btn btn-outline-primary" id="voice-btn" onclick="toggleVoiceRecording()" title="Voice Input">
                                    <i class="fas fa-microphone" id="voice-icon"></i>
                                </button>
                                <button class="btn btn-primary" onclick="sendChatMessage()">
                                    <i class="fas fa-paper-plane me-2"></i>Send
                                </button>
                            </div>
                            <div id="voice-status" class="mt-2" style="display: none;">
                                <small class="text-muted">
                                    <i class="fas fa-circle text-danger me-1" id="recording-indicator"></i>
                                    <span id="voice-status-text">Listening...</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card border-gradient mb-3">
                        <div class="card-header">
                            <h5><i class="fas fa-lightbulb me-2"></i>Quick Questions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="askQuickQuestion('What are the key cultural differences between Gen Z and Millennials in marketing?')">
                                    Gen Z vs Millennials
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="askQuickQuestion('How do cultural preferences vary between urban and rural markets?')">
                                    Urban vs Rural Markets
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="askQuickQuestion('What are the emerging cultural trends in digital marketing?')">
                                    Digital Marketing Trends
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="askQuickQuestion('How can I make my campaign more culturally inclusive?')">
                                    Cultural Inclusivity
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="card border-gradient">
                        <div class="card-header">
                            <h5><i class="fas fa-globe me-2"></i>Cross-Cultural Tools</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-success btn-sm w-100 mb-2" onclick="showCrossculturalAdapter()">
                                <i class="fas fa-exchange-alt me-2"></i>Campaign Adapter
                            </button>
                            <button class="btn btn-info btn-sm w-100 mb-2" onclick="showTrendAnalyzer()">
                                <i class="fas fa-chart-line me-2"></i>Trend Analyzer
                            </button>
                            <button class="btn btn-warning btn-sm w-100" onclick="showCulturalRiskAssessment()">
                                <i class="fas fa-shield-alt me-2"></i>Risk Assessment
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cross-Cultural Campaign Adapter -->
        <div id="crosscultural-adapter-section" class="campaign-adapter-section" style="display: none;">
            <div class="container-fluid px-4">
                <!-- Header Section -->
                <div class="row justify-content-center mb-5">
                    <div class="col-lg-10 col-xl-8">
                        <div class="text-center">
                            <div class="adapter-header-icon mb-4">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <h1 class="adapter-main-title mb-3">Cross-Cultural Campaign Adapter</h1>
                            <p class="adapter-subtitle">Transform your campaigns for global audiences with AI-powered cultural intelligence</p>
                        </div>
                    </div>
                </div>

                <!-- Main Form Section -->
                <div class="row justify-content-center">
                    <div class="col-lg-11 col-xl-10">
                        <div class="adapter-main-card">
                            <div class="adapter-card-header">
                                <div class="d-flex align-items-center">
                                    <div class="adapter-header-icon-small me-3">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <h3 class="mb-0">Original Campaign</h3>
                                </div>
                            </div>

                            <div class="adapter-card-body">
                                <form id="adaptation-form">
                                    <!-- Campaign Description Section -->
                                    <div class="adapter-section mb-5">
                                        <div class="adapter-form-group">
                                            <label for="original-campaign" class="adapter-form-label">
                                                <i class="fas fa-pen-fancy me-2"></i>
                                                Campaign Description
                                            </label>
                                            <textarea class="adapter-form-control" id="original-campaign" rows="5"
                                                      placeholder="Describe your campaign strategy, messaging, target audience, and key elements in detail..." required></textarea>
                                        </div>
                                    </div>

                                    <!-- Regions Selection Section -->
                                    <div class="adapter-section mb-5">
                                        <div class="row g-4">
                                            <!-- Source Region -->
                                            <div class="col-lg-6">
                                                <div class="adapter-region-card">
                                                    <div class="adapter-region-header">
                                                        <i class="fas fa-map-marker-alt me-2"></i>
                                                        <span>Source Region</span>
                                                    </div>
                                                    <div class="adapter-region-body">
                                                        <select class="adapter-form-control adapter-region-select" id="source-region" required>
                                                            <option value="">Select your campaign's origin region...</option>
                                                            <option value="North America">🇺🇸 North America</option>
                                                            <option value="Europe">🇪🇺 Europe</option>
                                                            <option value="Asia-Pacific">🌏 Asia-Pacific</option>
                                                            <option value="Latin America">🌎 Latin America</option>
                                                            <option value="Middle East">🕌 Middle East</option>
                                                            <option value="Africa">🌍 Africa</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Target Regions -->
                                            <div class="col-lg-6">
                                                <div class="adapter-region-card">
                                                    <div class="adapter-region-header">
                                                        <i class="fas fa-globe me-2"></i>
                                                        <span>Target Regions</span>
                                                    </div>
                                                    <div class="adapter-region-body">
                                                        <select class="adapter-form-control adapter-region-select" id="target-regions" multiple required>
                                                            <option value="North America">🇺🇸 North America</option>
                                                            <option value="Europe">🇪🇺 Europe</option>
                                                            <option value="Asia-Pacific">🌏 Asia-Pacific</option>
                                                            <option value="Latin America">🌎 Latin America</option>
                                                            <option value="Middle East">🕌 Middle East</option>
                                                            <option value="Africa">🌍 Africa</option>
                                                        </select>
                                                        <div class="adapter-help-text">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            Hold Ctrl/Cmd to select multiple target regions
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Generate Button Section -->
                                    <div class="adapter-section text-center">
                                        <button type="submit" class="adapter-btn-generate">
                                            <span class="btn-icon">
                                                <i class="fas fa-magic"></i>
                                            </span>
                                            <span class="btn-text">Generate Cultural Adaptations</span>
                                            <span class="btn-shine"></span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Section -->
                <div class="row justify-content-center">
                    <div class="col-lg-11 col-xl-10">
                        <div id="adaptation-results" class="adaptation-results-card" style="display: none;">
                            <div class="adapter-card-header">
                                <div class="d-flex align-items-center">
                                    <div class="adapter-header-icon-small me-3">
                                        <i class="fas fa-globe"></i>
                                    </div>
                                    <h3 class="mb-0">Cultural Adaptations</h3>
                                </div>
                            </div>
                            <div class="adapter-card-body">
                                <div id="adaptation-content"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cultural Trend Analyzer -->
        <div id="trend-analyzer-section" class="trend-analyzer-section" style="display: none;">
            <div class="container">
                <div class="row">
                    <div class="col-12 mb-5">
                        <div class="text-center">
                            <h2 class="text-gradient animate-fade-in mb-3">
                                <i class="fas fa-chart-line me-3"></i>Cultural Trend Analyzer
                            </h2>
                            <p class="lead text-muted">Discover emerging cultural trends and unlock marketing opportunities with AI-powered insights</p>
                        </div>
                    </div>
                </div>

                <!-- Analysis Settings - Full Width Landscape Layout -->
                <div class="row g-4 mb-4">
                    <div class="col-12">
                        <div class="trend-settings-card">
                            <div class="card-header" style="background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%); color: white; border: none; padding: 2rem;">
                                <h4 class="mb-0"><i class="fas fa-cog me-2"></i>Analysis Settings</h4>
                            </div>
                            <div class="card-body p-4">
                                <form id="trend-form">
                                    <div class="row g-4">
                                        <!-- Target Region and Timeframe - Side by Side -->
                                        <div class="col-md-6">
                                            <div class="adapter-form-group">
                                                <label for="trend-region" class="adapter-form-label">
                                                    <i class="fas fa-globe"></i>
                                                    Target Region
                                                </label>
                                                <div class="adapter-region-select">
                                                    <select class="adapter-form-control" id="trend-region" required>
                                                        <option value="global">🌍 Global Trends</option>
                                                        <option value="North America">🇺🇸 North America</option>
                                                        <option value="Europe">🇪🇺 Europe</option>
                                                        <option value="Asia-Pacific">🌏 Asia-Pacific</option>
                                                        <option value="Latin America">🌎 Latin America</option>
                                                        <option value="Middle East">🕌 Middle East</option>
                                                        <option value="Africa">🌍 Africa</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="adapter-form-group">
                                                <label for="trend-timeframe" class="adapter-form-label">
                                                    <i class="fas fa-clock"></i>
                                                    Analysis Timeframe
                                                </label>
                                                <div class="adapter-region-select">
                                                    <select class="adapter-form-control" id="trend-timeframe" required>
                                                        <option value="3months">📅 Last 3 Months</option>
                                                        <option value="6months" selected>📅 Last 6 Months</option>
                                                        <option value="1year">📅 Last Year</option>
                                                        <option value="2years">📅 Last 2 Years</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Trend Categories - Full Width -->
                                        <div class="col-12">
                                            <div class="adapter-form-group">
                                                <label class="adapter-form-label">
                                                    <i class="fas fa-tags"></i>
                                                    Trend Categories
                                                </label>
                                                <div class="trend-category-grid">
                                                    <div class="trend-category-item selected" data-category="music">
                                                        <div class="trend-category-content">
                                                            <i class="fas fa-music trend-category-icon"></i>
                                                            <label class="trend-category-label">Music</label>
                                                        </div>
                                                        <input type="checkbox" value="music" id="cat-music" checked style="display: none;">
                                                    </div>
                                                    <div class="trend-category-item selected" data-category="food">
                                                        <div class="trend-category-content">
                                                            <i class="fas fa-utensils trend-category-icon"></i>
                                                            <label class="trend-category-label">Food</label>
                                                        </div>
                                                        <input type="checkbox" value="food" id="cat-food" checked style="display: none;">
                                                    </div>
                                                    <div class="trend-category-item selected" data-category="fashion">
                                                        <div class="trend-category-content">
                                                            <i class="fas fa-tshirt trend-category-icon"></i>
                                                            <label class="trend-category-label">Fashion</label>
                                                        </div>
                                                        <input type="checkbox" value="fashion" id="cat-fashion" checked style="display: none;">
                                                    </div>
                                                    <div class="trend-category-item selected" data-category="lifestyle">
                                                        <div class="trend-category-content">
                                                            <i class="fas fa-heart trend-category-icon"></i>
                                                            <label class="trend-category-label">Lifestyle</label>
                                                        </div>
                                                        <input type="checkbox" value="lifestyle" id="cat-lifestyle" checked style="display: none;">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Analyze Button - Centered -->
                                        <div class="col-12 text-center">
                                            <button type="submit" class="trend-btn-analyze">
                                                <i class="fas fa-search me-2"></i>Analyze Cultural Trends
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Section - Full Width -->
                <div class="row g-4">
                    <div class="col-12">
                        <div id="trend-results" class="trend-results-card" style="display: none;">
                            <div class="card-header" style="background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%); color: white; border: none; padding: 2rem;">
                                <h4 class="mb-0"><i class="fas fa-trending-up me-2"></i>Trend Analysis Results</h4>
                            </div>
                            <div class="card-body p-4">
                                <div id="trend-content"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Value Calculator -->
        <div id="business-value-section" class="business-value-section" style="display: none;">
            <div class="container">
                <div class="row">
                    <div class="col-12 mb-5">
                        <div class="text-center">
                            <h2 class="text-gradient animate-fade-in mb-3">
                                <i class="fas fa-calculator me-3"></i>Business Value Calculator
                            </h2>
                            <p class="lead text-muted">Quantify the ROI of cultural intelligence and avoid costly missteps</p>
                        </div>
                    </div>
                </div>

                <div class="row g-4">
                    <!-- ROI Calculator -->
                    <div class="col-lg-6">
                        <div class="card border-gradient h-100">
                            <div class="card-header bg-gradient-primary text-white">
                                <h4 class="mb-0"><i class="fas fa-chart-line me-2"></i>ROI Calculator</h4>
                            </div>
                            <div class="card-body">
                                <form id="roi-calculator-form">
                                    <div class="mb-3">
                                        <label for="marketing-budget" class="form-label">
                                            <i class="fas fa-dollar-sign me-1"></i>Annual Marketing Budget (USD)
                                        </label>
                                        <input type="number" class="form-control" id="marketing-budget"
                                               placeholder="e.g., 1000000" min="1000" step="1000" required>
                                        <small class="form-text text-muted">Total yearly marketing spend</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="target-markets-count" class="form-label">
                                            <i class="fas fa-globe me-1"></i>Number of Target Markets
                                        </label>
                                        <input type="number" class="form-control" id="target-markets-count"
                                               placeholder="e.g., 5" min="1" max="20" required>
                                        <small class="form-text text-muted">International markets you're targeting</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="company-size" class="form-label">
                                            <i class="fas fa-users me-1"></i>Company Size
                                        </label>
                                        <select class="form-select" id="company-size" required>
                                            <option value="">Select company size...</option>
                                            <option value="startup">Startup (1-50 employees)</option>
                                            <option value="small">Small Business (51-200 employees)</option>
                                            <option value="medium">Medium Enterprise (201-1000 employees)</option>
                                            <option value="large">Large Enterprise (1000+ employees)</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="industry-type" class="form-label">
                                            <i class="fas fa-building me-1"></i>Industry Type
                                        </label>
                                        <select class="form-select" id="industry-type" required>
                                            <option value="">Select your industry...</option>
                                            <optgroup label="Technology & Digital">
                                                <option value="software">Software & SaaS</option>
                                                <option value="fintech">Financial Technology</option>
                                                <option value="ecommerce">E-commerce & Marketplaces</option>
                                                <option value="gaming">Gaming & Entertainment Tech</option>
                                                <option value="ai">AI & Machine Learning</option>
                                                <option value="cybersecurity">Cybersecurity</option>
                                            </optgroup>
                                            <optgroup label="Consumer Goods & Retail">
                                                <option value="fashion">Fashion & Apparel</option>
                                                <option value="beauty">Beauty & Cosmetics</option>
                                                <option value="luxury">Luxury Goods</option>
                                                <option value="retail">General Retail</option>
                                                <option value="sports">Sports & Fitness</option>
                                                <option value="toys">Toys & Games</option>
                                            </optgroup>
                                            <optgroup label="Food & Beverage">
                                                <option value="food">Food & Restaurants</option>
                                                <option value="beverage">Beverages & Alcohol</option>
                                                <option value="organic">Organic & Health Foods</option>
                                                <option value="fastfood">Fast Food & QSR</option>
                                            </optgroup>
                                            <optgroup label="Media & Entertainment">
                                                <option value="streaming">Streaming & Media</option>
                                                <option value="music">Music & Audio</option>
                                                <option value="publishing">Publishing & Content</option>
                                                <option value="social">Social Media Platforms</option>
                                                <option value="advertising">Advertising & Marketing</option>
                                            </optgroup>
                                            <optgroup label="Travel & Hospitality">
                                                <option value="travel">Travel & Tourism</option>
                                                <option value="hospitality">Hotels & Hospitality</option>
                                                <option value="airlines">Airlines & Transportation</option>
                                                <option value="experiences">Experiences & Events</option>
                                            </optgroup>
                                            <optgroup label="Healthcare & Wellness">
                                                <option value="healthcare">Healthcare Services</option>
                                                <option value="pharma">Pharmaceuticals</option>
                                                <option value="wellness">Wellness & Fitness</option>
                                                <option value="mental">Mental Health</option>
                                            </optgroup>
                                            <optgroup label="Financial Services">
                                                <option value="banking">Banking & Finance</option>
                                                <option value="insurance">Insurance</option>
                                                <option value="investment">Investment & Trading</option>
                                                <option value="crypto">Cryptocurrency & Blockchain</option>
                                            </optgroup>
                                            <optgroup label="Education & Professional">
                                                <option value="education">Education & E-learning</option>
                                                <option value="consulting">Consulting Services</option>
                                                <option value="legal">Legal Services</option>
                                                <option value="hr">Human Resources</option>
                                            </optgroup>
                                            <optgroup label="Automotive & Manufacturing">
                                                <option value="automotive">Automotive</option>
                                                <option value="manufacturing">Manufacturing</option>
                                                <option value="electronics">Electronics & Hardware</option>
                                                <option value="energy">Energy & Utilities</option>
                                            </optgroup>
                                            <optgroup label="Real Estate & Construction">
                                                <option value="realestate">Real Estate</option>
                                                <option value="construction">Construction</option>
                                                <option value="architecture">Architecture & Design</option>
                                            </optgroup>
                                            <optgroup label="Non-Profit & Government">
                                                <option value="nonprofit">Non-Profit Organizations</option>
                                                <option value="government">Government & Public Sector</option>
                                                <option value="ngo">NGOs & Social Impact</option>
                                            </optgroup>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="business-idea" class="form-label">
                                            <i class="fas fa-lightbulb me-1"></i>Business Idea / Product Description
                                        </label>
                                        <textarea class="form-control" id="business-idea" rows="3"
                                                  placeholder="Describe your business idea, product, or service that you want to analyze for cultural intelligence ROI..."
                                                  style="resize: vertical; min-height: 80px;"></textarea>
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>Provide details about your business concept for AI-powered analysis with Qloo + Gemini APIs
                                        </small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="industry-risk" class="form-label">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Cultural Sensitivity Level
                                        </label>
                                        <select class="form-select" id="industry-risk" required>
                                            <option value="">Select sensitivity level...</option>
                                            <option value="low">🟢 Low Sensitivity (B2B Tech, Software, Cybersecurity)</option>
                                            <option value="medium">🟡 Medium Sensitivity (Retail, Automotive, Finance)</option>
                                            <option value="high">🔴 High Sensitivity (Food, Fashion, Entertainment, Beauty)</option>
                                            <option value="critical">🚨 Critical Sensitivity (Religion, Politics, Social Issues)</option>
                                        </select>
                                        <small class="form-text text-muted">How sensitive is your industry to cultural differences?</small>
                                    </div>

                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-calculator me-2"></i>Calculate Business Value
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- ROI Results -->
                    <div class="col-lg-6">
                        <div id="roi-results" class="card border-gradient h-100" style="display: none;">
                            <div class="card-header bg-gradient-success text-white">
                                <h4 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Business Impact Analysis</h4>
                            </div>
                            <div class="card-body">
                                <div id="roi-content"></div>
                            </div>
                        </div>

                        <!-- Placeholder when no results -->
                        <div id="roi-placeholder" class="card border-gradient h-100">
                            <div class="card-body d-flex align-items-center justify-content-center text-center">
                                <div>
                                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Calculate ROI</h5>
                                    <p class="text-muted">Fill out the form to see your business value analysis</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 🏆 HACKATHON ENHANCED: Advanced ROI Calculator -->
                <div class="row mt-5">
                    <div class="col-12">
                        <div class="card border-gradient">
                            <div class="card-header bg-gradient-warning text-white">
                                <h4 class="mb-0">
                                    <i class="fas fa-calculator me-2"></i>🏆 Enhanced ROI Calculator
                                </h4>
                                <p class="mb-0 mt-2">Advanced business value calculator with cultural intelligence metrics</p>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="enhanced-roi-form">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6>Business Parameters</h6>
                                                <button type="button" class="btn btn-sm btn-outline-primary" id="advanced-options-toggle">
                                                    <i class="fas fa-chevron-down me-2"></i>Show Advanced Options
                                                </button>
                                            </div>

                                            <!-- Quick ROI Estimate Display -->
                                            <div id="quick-roi-estimate" class="mb-3"></div>

                                            <!-- Advanced Options (Hidden by default) -->
                                            <div id="advanced-options" style="display: none;">
                                                <div class="row">
                                                    <div class="col-md-6 mb-3">
                                                        <label class="form-label">Cultural Intelligence Level</label>
                                                        <select class="form-select" id="cultural-intelligence-level">
                                                            <option value="basic">Basic</option>
                                                            <option value="standard" selected>Standard</option>
                                                            <option value="advanced">Advanced</option>
                                                            <option value="expert">Expert</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-6 mb-3">
                                                        <label class="form-label">Competitor Pressure</label>
                                                        <select class="form-select" id="competitor-pressure">
                                                            <option value="low">Low</option>
                                                            <option value="medium" selected>Medium</option>
                                                            <option value="high">High</option>
                                                            <option value="very_high">Very High</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-12 mb-3">
                                                        <label class="form-label">Expansion Goals</label>
                                                        <select class="form-select" id="expansion-goals">
                                                            <option value="conservative">Conservative</option>
                                                            <option value="moderate" selected>Moderate</option>
                                                            <option value="aggressive">Aggressive</option>
                                                            <option value="very_aggressive">Very Aggressive</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div id="enhanced-roi-preview" class="enhanced-roi-preview">
                                            <div class="text-center text-muted py-4">
                                                <i class="fas fa-chart-pie fa-3x mb-3"></i>
                                                <h6>Enhanced ROI Analysis</h6>
                                                <p>Complete the form to see comprehensive business value metrics</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Case Studies Section -->
                <div class="row mt-5">
                    <div class="col-12">
                        <div class="card border-gradient shadow-lg">
                            <div class="card-header text-white" style="background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 20px 20px 0 0;">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div>
                                        <h4 class="mb-1 fw-bold">
                                            <i class="fas fa-trophy me-2"></i>Success Stories & Case Studies
                                        </h4>
                                        <p class="mb-0 opacity-90">Real-world examples powered by Qloo + Gemini AI integration</p>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-light text-dark px-3 py-2 rounded-pill">
                                            <i class="fas fa-chart-line me-1"></i>
                                            Live Data
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-4" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95)); backdrop-filter: blur(20px);">
                                <div class="row mb-4">
                                    <div class="col-md-8">
                                        <p class="lead mb-0 text-secondary">Premium case studies showcasing how Qloo + Gemini AI integration drives exceptional business results</p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="d-flex align-items-center justify-content-end gap-3">
                                            <div class="text-center">
                                                <div class="fw-bold text-success fs-5">100%</div>
                                                <small class="text-muted">Success Rate</small>
                                            </div>
                                            <div class="text-center">
                                                <div class="fw-bold text-info fs-5">2</div>
                                                <small class="text-muted">Premium Cases</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row g-4" id="case-studies-grid">
                                    <!-- Enhanced case studies will be populated by JavaScript -->
                                    <div class="col-12 text-center py-5" id="case-studies-loading">
                                        <div class="loading-spinner mx-auto mb-3">
                                            <div class="spinner-ring"></div>
                                            <div class="spinner-ring"></div>
                                            <div class="spinner-ring"></div>
                                        </div>
                                        <p class="text-muted">Loading premium case studies with enhanced UI...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Market Expansion Visualizer -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card border-gradient">
                            <div class="card-header bg-gradient-warning text-white">
                                <h4 class="mb-0"><i class="fas fa-expand-arrows-alt me-2"></i>Market Expansion Opportunities</h4>
                            </div>
                            <div class="card-body">
                                <div id="market-expansion-content">
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Calculate ROI above to see market expansion opportunities
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Insights Dashboard Section -->
        <div id="insights-section" class="insights-dashboard" style="display: none;">
            <!-- Hero Header -->
            <div class="insights-hero">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <h1 class="insights-title" style="color: #2c3e50 !important; font-weight: 700;">
                                <i class="fas fa-chart-bar me-3" style="color: #667eea !important;"></i>Visual Insights Dashboard
                            </h1>
                            <p class="insights-subtitle" style="color: #64748b !important;">Real-time analytics powered by AI and cultural intelligence</p>
                        </div>
                        <div class="col-lg-4 text-end">
                            <div class="insights-controls">
                                <button class="btn btn-insights-primary" onclick="loadInsightsDashboard()">
                                    <i class="fas fa-sync-alt me-2"></i>Refresh Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="container-fluid px-4">

            <!-- Dashboard Stats Cards -->
            <div class="row mb-5">
                <div class="col-md-3 mb-4">
                    <div class="card text-center animate-float">
                        <div class="card-body">
                            <div class="feature-icon" style="width: 60px; height: 60px; font-size: 1.5rem; margin-bottom: 1rem;">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3 class="status-success text-glow" id="total-personas">0</h3>
                            <p class="text-muted mb-0">Total Personas</p>
                            <small class="text-success">
                                <i class="fas fa-check-circle"></i> Real APIs
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card text-center animate-float" style="animation-delay: 0.2s;">
                        <div class="card-body">
                            <div class="feature-icon" style="width: 60px; height: 60px; font-size: 1.5rem; margin-bottom: 1rem;">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                            <h3 class="status-warning text-glow" id="total-campaigns">0</h3>
                            <p class="text-muted mb-0">Campaigns Analyzed</p>
                            <small class="text-success">
                                <i class="fas fa-check-circle"></i> Real APIs
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card text-center animate-float" style="animation-delay: 0.4s;">
                        <div class="card-body">
                            <div class="feature-icon" style="width: 60px; height: 60px; font-size: 1.5rem; margin-bottom: 1rem;">
                                <i class="fas fa-globe"></i>
                            </div>
                            <h3 class="status-success text-glow" id="total-regions">0</h3>
                            <p class="text-muted mb-0">Regions Covered</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card text-center animate-float" style="animation-delay: 0.6s;">
                        <div class="card-body">
                            <div class="feature-icon" style="width: 60px; height: 60px; font-size: 1.5rem; margin-bottom: 1rem;">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h3 class="status-success text-glow" id="avg-score">0</h3>
                            <p class="text-muted mb-0">Avg. Alignment Score</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Charts Row -->
            <div class="row mb-5">
                <div class="col-lg-6 mb-4">
                    <div class="insights-chart-card">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-chart-pie me-2"></i>Demographics Breakdown
                            </div>
                            <div class="chart-controls">
                                <button class="btn btn-chart-control" onclick="refreshChart('demographics')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <div id="demographics-chart" class="chart-container"></div>
                            <div class="chart-insights">
                                <div class="insight-item">
                                    <span class="insight-label">Most Popular:</span>
                                    <span class="insight-value">Millennials (34%)</span>
                                </div>
                                <div class="insight-item">
                                    <span class="insight-label">Growth Leader:</span>
                                    <span class="insight-value">Gen Z (+15%)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="insights-chart-card">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-globe me-2"></i>Global Regions Distribution
                            </div>
                            <div class="chart-controls">
                                <button class="btn btn-chart-control" onclick="refreshChart('regions')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <div id="regions-chart" class="chart-container"></div>
                            <div class="chart-insights">
                                <div class="insight-item">
                                    <span class="insight-label">Top Region:</span>
                                    <span class="insight-value">North America (28%)</span>
                                </div>
                                <div class="insight-item">
                                    <span class="insight-label">Fastest Growing:</span>
                                    <span class="insight-value">Asia-Pacific (+22%)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Analytics Row -->
            <div class="row mb-5">
                <div class="col-lg-8 mb-4">
                    <div class="insights-chart-card timeline-card">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-chart-line me-2"></i>Cultural Trends Timeline
                            </div>
                            <div class="chart-controls">
                                <select class="form-select form-select-sm me-2" style="width: auto;">
                                    <option>Last 30 Days</option>
                                    <option>Last 3 Months</option>
                                    <option>Last Year</option>
                                </select>
                                <button class="btn btn-chart-control" onclick="refreshChart('timeline')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <div id="timeline-chart" class="chart-container-large"></div>
                            <div class="timeline-insights">
                                <div class="timeline-stat">
                                    <span class="stat-number">+47%</span>
                                    <span class="stat-label">Growth This Month</span>
                                </div>
                                <div class="timeline-stat">
                                    <span class="stat-number">1,247</span>
                                    <span class="stat-label">Peak Daily Activity</span>
                                </div>
                                <div class="timeline-stat">
                                    <span class="stat-number">89%</span>
                                    <span class="stat-label">Accuracy Rate</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 mb-4">
                    <div class="insights-chart-card taste-card">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-fire me-2"></i>Trending Taste Patterns
                            </div>
                            <div class="chart-controls">
                                <button class="btn btn-chart-control" onclick="refreshChart('taste')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <div id="taste-patterns-chart" class="chart-container"></div>
                            <div class="taste-insights">
                                <div class="taste-item trending">
                                    <span class="taste-icon">🎵</span>
                                    <span class="taste-name">K-Pop</span>
                                    <span class="taste-trend">+23%</span>
                                </div>
                                <div class="taste-item trending">
                                    <span class="taste-icon">🍜</span>
                                    <span class="taste-name">Asian Fusion</span>
                                    <span class="taste-trend">+18%</span>
                                </div>
                                <div class="taste-item trending">
                                    <span class="taste-icon">👕</span>
                                    <span class="taste-name">Streetwear</span>
                                    <span class="taste-trend">+15%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Analytics Row -->
            <div class="row mb-5">
                <div class="col-lg-6 mb-4">
                    <div class="insights-chart-card">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-globe me-2"></i>Global Distribution Heatmap
                                <span class="badge bg-success ms-2">
                                    <i class="fas fa-check-circle"></i> Real APIs
                                </span>
                            </div>
                            <div class="chart-controls">
                                <button class="btn btn-chart-control" onclick="refreshChart('geographic')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <div id="geographic-heatmap" class="chart-container"></div>
                            <div class="chart-insights">
                                <div class="insight-item">
                                    <span class="insight-label">Hottest Region:</span>
                                    <span class="insight-value">California (USA)</span>
                                </div>
                                <div class="insight-item">
                                    <span class="insight-label">Emerging Market:</span>
                                    <span class="insight-value">Southeast Asia</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="insights-chart-card">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-chart-line me-2"></i>Cultural Trends Evolution
                            </div>
                            <div class="chart-controls">
                                <button class="btn btn-chart-control" onclick="refreshChart('trends')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <div id="trend-timeline" class="chart-container"></div>
                            <div class="chart-insights">
                                <div class="insight-item">
                                    <span class="insight-label">Trending Now:</span>
                                    <span class="insight-value">Sustainable Fashion</span>
                                </div>
                                <div class="insight-item">
                                    <span class="insight-label">Growth Rate:</span>
                                    <span class="insight-value">+34% this quarter</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Analytics Row -->
            <div class="row mb-5">
                <div class="col-lg-6 mb-4">
                    <div class="insights-chart-card">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-project-diagram me-2"></i>Taste Correlation Matrix
                            </div>
                            <div class="chart-controls">
                                <button class="btn btn-chart-control" onclick="refreshChart('correlation')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <div id="correlation-matrix" class="chart-container"></div>
                            <div class="chart-insights">
                                <div class="insight-item">
                                    <span class="insight-label">Strongest Link:</span>
                                    <span class="insight-value">Music ↔ Fashion (0.87)</span>
                                </div>
                                <div class="insight-item">
                                    <span class="insight-label">Surprise Connection:</span>
                                    <span class="insight-value">Food ↔ Travel (0.72)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="insights-chart-card">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-crystal-ball me-2"></i>AI Predictive Analytics
                            </div>
                            <div class="chart-controls">
                                <button class="btn btn-chart-control" onclick="refreshChart('predictive')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <div id="predictive-analytics" class="chart-container"></div>
                            <div class="chart-insights">
                                <div class="insight-item">
                                    <span class="insight-label">Next Trend:</span>
                                    <span class="insight-value">AI-Generated Art</span>
                                </div>
                                <div class="insight-item">
                                    <span class="insight-label">Confidence:</span>
                                    <span class="insight-value">94% accuracy</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            </div> <!-- Close container-fluid -->

            <!-- Enhanced Summary Stats -->
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card bg-gradient-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Trend Score</h6>
                                    <h3 id="trend-score">--</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-trending-up fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card bg-gradient-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Diversity Index</h6>
                                    <h3 id="diversity-index">--</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-diversity fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card bg-gradient-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Market Coverage</h6>
                                    <h3 id="market-coverage">--</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-map fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card bg-gradient-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Prediction Accuracy</h6>
                                    <h3 id="prediction-accuracy">92%</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-bullseye fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Enhanced Personas Overview Section -->
        <div id="personas-section" class="container" style="display: none;">
            <!-- Header with Actions -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <h2 class="mb-3">
                        <i class="fas fa-users me-2 text-primary"></i>
                        Persona Overview
                        <span id="persona-count-badge" class="badge bg-primary ms-2">0</span>
                    </h2>
                    <p class="text-muted">Explore your generated personas and analyze their potential for campaigns</p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="refreshPersonas()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                        <button type="button" class="btn btn-primary" onclick="showCampaignAnalysis()">
                            <i class="fas fa-chart-line me-1"></i>Analyze Campaign
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filter and Sort Controls -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="persona-search" placeholder="Search personas by region or demographic...">
                    </div>
                </div>
                <div class="col-lg-3">
                    <select class="form-select" id="region-filter">
                        <option value="">All Regions</option>
                    </select>
                </div>
                <div class="col-lg-3">
                    <select class="form-select" id="demographic-filter">
                        <option value="">All Demographics</option>
                    </select>
                </div>
            </div>

            <!-- Personas Grid -->
            <div id="personas-list" class="row g-4"></div>

            <!-- Empty State -->
            <div id="personas-empty-state" class="text-center py-5" style="display: none;">
                <div class="mb-4">
                    <i class="fas fa-user-plus fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">No Personas Generated Yet</h4>
                    <p class="text-muted">Start by generating your first persona to see insights and analytics</p>
                    <button class="btn btn-primary btn-lg" onclick="showSection('generator')">
                        <i class="fas fa-plus me-2"></i>Generate First Persona
                    </button>
                </div>
            </div>
        </div>

        <!-- Campaign Analysis Modal -->
        <div class="modal fade" id="campaignAnalysisModal" tabindex="-1" aria-labelledby="campaignAnalysisModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-gradient-primary text-white">
                        <h5 class="modal-title" id="campaignAnalysisModalLabel">
                            <i class="fas fa-chart-line me-2"></i>Campaign Analysis
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Campaign Selection -->
                        <div class="row mb-4">
                            <div class="col-lg-6">
                                <label for="campaign-select" class="form-label">Select Campaign to Analyze</label>
                                <select class="form-select" id="campaign-select">
                                    <option value="">Choose a sample campaign...</option>
                                    <option value="tech-product-launch">Tech Product Launch - Global Smartphone</option>
                                    <option value="fashion-summer-collection">Fashion Summer Collection - Sustainable Clothing</option>
                                    <option value="food-delivery-expansion">Food Delivery Service - Market Expansion</option>
                                    <option value="fitness-app-promotion">Fitness App - Health & Wellness Campaign</option>
                                    <option value="travel-destination-marketing">Travel Destination - Cultural Tourism</option>
                                </select>
                            </div>
                            <div class="col-lg-6">
                                <label for="target-personas" class="form-label">Target Personas</label>
                                <select class="form-select" id="target-personas" multiple>
                                    <option value="all">All Generated Personas</option>
                                </select>
                                <small class="text-muted">Hold Ctrl/Cmd to select multiple personas</small>
                            </div>
                        </div>

                        <!-- Analysis Button -->
                        <div class="text-center mb-4">
                            <button class="btn btn-primary btn-lg" onclick="analyzeCampaign()">
                                <i class="fas fa-magic me-2"></i>Analyze Campaign Effectiveness
                            </button>
                        </div>

                        <!-- Analysis Results -->
                        <div id="campaign-analysis-results" style="display: none;">
                            <!-- Campaign Overview -->
                            <div class="row mb-4">
                                <div class="col-lg-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h3 id="effectiveness-score">--</h3>
                                            <p class="mb-0">Effectiveness Score</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h3 id="reach-potential">--</h3>
                                            <p class="mb-0">Reach Potential</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h3 id="engagement-rate">--</h3>
                                            <p class="mb-0">Est. Engagement</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h3 id="roi-estimate">--</h3>
                                            <p class="mb-0">ROI Estimate</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Detailed Analysis -->
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-bullseye me-2"></i>Persona Alignment</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="persona-alignment-chart" style="height: 300px;"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-lightbulb me-2"></i>AI Recommendations</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="ai-recommendations">
                                                <div class="d-flex justify-content-center">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">Generating recommendations...</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="exportCampaignAnalysis()">
                            <i class="fas fa-download me-2"></i>Export Analysis
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Campaign Analysis Modal -->
        <div class="modal fade" id="campaignAnalysisModal" tabindex="-1" aria-labelledby="campaignAnalysisModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-gradient-primary text-white">
                        <h5 class="modal-title" id="campaignAnalysisModalLabel">
                            <i class="fas fa-chart-line me-2"></i>Campaign Analysis
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Campaign Selection -->
                        <div class="row mb-4">
                            <div class="col-lg-6">
                                <label for="campaign-select" class="form-label">Select Campaign to Analyze</label>
                                <select class="form-select" id="campaign-select">
                                    <option value="">Choose a sample campaign...</option>
                                    <option value="tech-product-launch">Tech Product Launch - Global Smartphone</option>
                                    <option value="fashion-summer-collection">Fashion Summer Collection - Sustainable Clothing</option>
                                    <option value="food-delivery-expansion">Food Delivery Service - Market Expansion</option>
                                    <option value="fitness-app-promotion">Fitness App - Health & Wellness Campaign</option>
                                    <option value="travel-destination-marketing">Travel Destination - Cultural Tourism</option>
                                </select>
                            </div>
                            <div class="col-lg-6">
                                <label for="target-personas" class="form-label">Target Personas</label>
                                <select class="form-select" id="target-personas" multiple>
                                    <option value="all">All Generated Personas</option>
                                </select>
                                <small class="text-muted">Hold Ctrl/Cmd to select multiple personas</small>
                            </div>
                        </div>

                        <!-- Analysis Button -->
                        <div class="text-center mb-4">
                            <button class="btn btn-primary btn-lg" onclick="analyzeCampaign()">
                                <i class="fas fa-magic me-2"></i>Analyze Campaign Effectiveness
                            </button>
                        </div>

                        <!-- Analysis Results -->
                        <div id="campaign-analysis-results" style="display: none;">
                            <!-- Campaign Overview -->
                            <div class="row mb-4">
                                <div class="col-lg-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h3 id="effectiveness-score">--</h3>
                                            <p class="mb-0">Effectiveness Score</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h3 id="reach-potential">--</h3>
                                            <p class="mb-0">Reach Potential</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h3 id="engagement-rate">--</h3>
                                            <p class="mb-0">Est. Engagement</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h3 id="roi-estimate">--</h3>
                                            <p class="mb-0">ROI Estimate</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Detailed Analysis -->
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-bullseye me-2"></i>Persona Alignment</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="persona-alignment-chart" style="height: 300px;"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-lightbulb me-2"></i>AI Recommendations</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="ai-recommendations">
                                                <div class="d-flex justify-content-center">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">Generating recommendations...</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="exportCampaignAnalysis()">
                            <i class="fas fa-download me-2"></i>Export Analysis
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 🏆 WINNING FEATURES SECTION -->
        <div id="winning-features-section" class="container" style="display: none;">
            <div class="row">
                <div class="col-12 mb-4">
                    <h2 class="text-gradient animate-fade-in">
                        <i class="fas fa-trophy me-3"></i>🏆 Unique Features
                    </h2>
                    <p class="lead">Experience cutting-edge AI technology that sets TasteShift apart from the competition</p>
                </div>
            </div>

            <!-- Feature Categories -->
            <div class="row g-4 mb-5">
                <!-- Advanced Qloo Integration -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card h-100">
                        <div class="feature-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h4>Advanced Qloo Integration</h4>
                        <p>Real-time recommendations, cultural intelligence dashboard, and predictive analytics</p>
                    </div>
                </div>

                <!-- Intelligent LLM Enhancement -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card h-100">
                        <div class="feature-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h4>Intelligent LLM Enhancement</h4>
                        <p>Conversational AI, natural language insights, and context-aware recommendations</p>
                    </div>
                </div>

                <!-- Interactive Data Visualization -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card h-100">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4>Interactive Data Visualization</h4>
                        <p>Real-time dashboards, 3D landscapes, and exportable business reports</p>
                    </div>
                </div>

                <!-- Business Intelligence -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card h-100">
                        <div class="feature-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <h4>Business Intelligence Suite</h4>
                        <p>Market research, brand positioning, competitor analysis, and ROI calculations</p>
                    </div>
                </div>

                <!-- Technical Excellence -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card h-100">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h4>Technical Excellence</h4>
                        <p>Real-time monitoring, advanced caching, load balancing, and performance optimization</p>
                    </div>
                </div>

                <!-- Innovation & Originality -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card h-100">
                        <div class="feature-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h4>Innovation & Originality</h4>
                        <p>Voice interface, social sharing, and gamification</p>
                    </div>
                </div>
            </div>

            <!-- Comprehensive Demo Section -->
            <div class="row">
                <div class="col-12">
                    <div class="demo-section">
                        <h3 class="text-center mb-4">
                            <i class="fas fa-star me-2"></i>Experience All Features Together
                        </h3>
                        <div class="text-center">
                            <button class="btn btn-lg btn-gradient me-3" onclick="window.location.href='/onboarding'">
                                <i class="fas fa-play-circle me-2"></i>Run Comprehensive Demo
                            </button>
                            <button class="btn btn-lg btn-outline-primary" onclick="showAPIDocumentation()">
                                <i class="fas fa-code me-2"></i>View API Documentation
                            </button>
                        </div>
                        <div id="demo-results" class="mt-4" style="display: none;">
                            <div class="demo-output">
                                <h5>Demo Results:</h5>
                                <div id="demo-content"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Loading Modal with Body Scan Animation -->
        <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content" style="background: linear-gradient(135deg, #0f172a, #1e293b); border: none; border-radius: 20px;">
                    <div class="modal-body text-center py-5">
                        <!-- Body Scan Animation Container -->
                        <div class="body-scan-container mb-4" style="height: 300px; position: relative;">
                            <spline-viewer url="https://prod.spline.design/H0hnZcEJPfnHWIcZ/scene.splinecode"
                                           style="width: 100%; height: 100%; border-radius: 15px; background: transparent;"
                                           id="spline-viewer">
                            </spline-viewer>
                            <!-- Fallback animation if Spline doesn't load -->
                            <div class="fallback-animation" id="fallback-animation" style="display: none; width: 100%; height: 100%; position: absolute; top: 0; left: 0; border-radius: 15px;">
                                <div class="scanning-effect">
                                    <div class="human-silhouette">
                                        <i class="fas fa-user" style="font-size: 120px; color: #00d4ff; opacity: 0.8;"></i>
                                    </div>
                                    <div class="scan-lines"></div>
                                </div>
                            </div>
                        </div>

                        <h5 id="loading-text" class="mb-3" style="color: #ffffff !important; font-weight: 600; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">Scanning Personas...</h5>

                        <!-- Enhanced Progress Bar -->
                        <div class="progress mb-3" style="height: 8px; border-radius: 10px; background: rgba(255, 255, 255, 0.1);">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 style="background: linear-gradient(90deg, #00d4ff, #5b63f7, #ff006e); width: 0%;"
                                 id="loading-progress"></div>
                        </div>

                        <!-- Loading Steps -->
                        <div class="loading-steps mb-3">
                            <div class="step-item active" id="step-scan">
                                <i class="fas fa-user-circle me-2"></i>
                                <span id="loading-detail" style="color: #ffffff !important; font-weight: 500;">Scanning cultural preferences...</span>
                            </div>
                            <div class="step-item" id="step-analyze">
                                <i class="fas fa-brain me-2"></i>
                                <span>Analyzing behavioral patterns...</span>
                            </div>
                            <div class="step-item" id="step-generate">
                                <i class="fas fa-magic me-2"></i>
                                <span>Generating detailed persona...</span>
                            </div>
                        </div>

                        <!-- Animated Dots -->
                        <div class="loading-dots">
                            <span class="dot"></span>
                            <span class="dot"></span>
                            <span class="dot"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <style>
            .body-scan-container {
                background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, rgba(91, 99, 247, 0.1) 50%, rgba(255, 0, 110, 0.1) 100%);
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
            }

            .loading-steps {
                text-align: left;
                max-width: 300px;
                margin: 0 auto;
            }

            .step-item {
                color: rgba(255, 255, 255, 0.5);
                padding: 8px 0;
                transition: all 0.3s ease;
                font-size: 0.9rem;
            }

            .step-item.active {
                color: #00d4ff;
                transform: translateX(10px);
            }

            .step-item.completed {
                color: #22c55e;
            }

            .loading-dots {
                display: flex;
                justify-content: center;
                gap: 8px;
                margin-top: 20px;
            }

            .dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: linear-gradient(45deg, #00d4ff, #5b63f7);
                animation: loading-pulse 1.5s ease-in-out infinite;
            }

            .dot:nth-child(2) {
                animation-delay: 0.3s;
            }

            .dot:nth-child(3) {
                animation-delay: 0.6s;
            }

            @keyframes loading-pulse {
                0%, 100% {
                    opacity: 0.3;
                    transform: scale(1);
                }
                50% {
                    opacity: 1;
                    transform: scale(1.2);
                }
            }

            /* Fallback Animation Styles */
            .fallback-animation {
                display: flex;
                align-items: center;
                justify-content: center;
                background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, rgba(91, 99, 247, 0.1) 50%, rgba(255, 0, 110, 0.1) 100%);
            }

            .scanning-effect {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .human-silhouette {
                position: relative;
                z-index: 2;
                animation: pulse-glow 2s ease-in-out infinite;
            }

            .scan-lines {
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 200px;
                height: 100%;
                background: linear-gradient(
                    to bottom,
                    transparent 0%,
                    rgba(0, 212, 255, 0.3) 45%,
                    rgba(0, 212, 255, 0.8) 50%,
                    rgba(0, 212, 255, 0.3) 55%,
                    transparent 100%
                );
                animation: scan-sweep 3s ease-in-out infinite;
            }

            @keyframes pulse-glow {
                0%, 100% {
                    filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
                    transform: scale(1);
                }
                50% {
                    filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.8));
                    transform: scale(1.05);
                }
            }

            @keyframes scan-sweep {
                0% {
                    top: -20px;
                    opacity: 0;
                }
                10% {
                    opacity: 1;
                }
                90% {
                    opacity: 1;
                }
                100% {
                    top: 100%;
                    opacity: 0;
                }
            }



            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        </style>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/charts.js') }}"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>

    <script>
        // 🏆 WINNING FEATURES JAVASCRIPT FUNCTIONS

        function showWinningFeatures() {
            hideAllSections();
            document.getElementById('winning-features-section').style.display = 'block';
            updateActiveNav('winning-features');
        }

        function demoQlooFeatures() {
            showLoadingModal('Demonstrating Advanced Qloo Integration...');

            fetch('/api/real-time-recommendations', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    user_preferences: { region: 'Global', categories: ['music', 'food', 'fashion'] },
                    limit: 5
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingModal();
                showDemoResults('Qloo Integration Demo', data);
            })
            .catch(error => {
                hideLoadingModal();
                showError('Demo failed: ' + error.message);
            });
        }

        function demoLLMFeatures() {
            showLoadingModal('Demonstrating Intelligent LLM Enhancement...');

            fetch('/api/conversational-persona', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    user_input: 'Create a persona for tech-savvy millennials in Asia who love innovation'
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingModal();
                showDemoResults('LLM Enhancement Demo', data);
            })
            .catch(error => {
                hideLoadingModal();
                showError('Demo failed: ' + error.message);
            });
        }

        function demoVisualizationFeatures() {
            showLoadingModal('Demonstrating Interactive Data Visualization...');

            fetch('/api/real-time-dashboard', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    data_stream: [{ region: 'Global', trend: 'AI Innovation', score: 95 }],
                    update_interval: 5
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingModal();
                showDemoResults('Visualization Demo', data);
            })
            .catch(error => {
                hideLoadingModal();
                showError('Demo failed: ' + error.message);
            });
        }

        function demoBIFeatures() {
            showLoadingModal('Demonstrating Business Intelligence Suite...');

            fetch('/api/market-research', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    target_market: { region: 'Global', demographics: ['Millennials'] },
                    research_parameters: { industry: 'technology', timeframe: '12months' }
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingModal();
                showDemoResults('Business Intelligence Demo', data);
            })
            .catch(error => {
                hideLoadingModal();
                showError('Demo failed: ' + error.message);
            });
        }

        function demoTechnicalFeatures() {
            showLoadingModal('Demonstrating Technical Excellence...');

            fetch('/api/system-health', {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingModal();
                showDemoResults('Technical Excellence Demo', data);
            })
            .catch(error => {
                hideLoadingModal();
                showError('Demo failed: ' + error.message);
            });
        }

        function demoInnovationFeatures() {
            showLoadingModal('Demonstrating Innovation & Originality...');

            fetch('/api/innovation-suite', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    features: ['voice', 'ar', 'social', 'gamification'],
                    user_context: { user_id: 'demo_user', session_id: 'demo_session' },
                    voice_data: { audio_data: 'demo_audio' },
                    ar_data: { persona_data: { name: 'Demo Persona', region: 'Global' } },
                    social_data: { content_data: { title: 'Amazing Demo', description: 'TasteShift Innovation' }, platform: 'twitter' },
                    gamification_data: { action: 'award_xp', action_type: 'innovation_demo' }
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingModal();
                showDemoResults('Innovation Demo', data);
            })
            .catch(error => {
                hideLoadingModal();
                showError('Demo failed: ' + error.message);
            });
        }

        function runComprehensiveDemo() {
            showLoadingModal('Running Comprehensive Demo of All Features...');

            fetch('/api/demo/comprehensive', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    demo_config: { full_demo: true },
                    user_preferences: {
                        region: 'Global',
                        demographic: 'Millennials',
                        categories: ['music', 'food', 'fashion', 'technology']
                    }
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingModal();
                showComprehensiveDemoResults(data);
            })
            .catch(error => {
                hideLoadingModal();
                showError('Comprehensive demo failed: ' + error.message);
            });
        }

        function showAPIDocumentation() {
            showLoadingModal('Loading API Documentation...');

            fetch('/api/documentation', {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingModal();
                showAPIDocumentationModal(data);
            })
            .catch(error => {
                hideLoadingModal();
                showError('Failed to load documentation: ' + error.message);
            });
        }

        function showDemoResults(title, data) {
            const demoResults = document.getElementById('demo-results');
            const demoContent = document.getElementById('demo-content');

            demoContent.innerHTML = `
                <div class="demo-result-card">
                    <h6>${title}</h6>
                    <div class="demo-data">
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                </div>
            `;

            demoResults.style.display = 'block';
            demoResults.scrollIntoView({ behavior: 'smooth' });
        }

        function showComprehensiveDemoResults(data) {
            const demoResults = document.getElementById('demo-results');
            const demoContent = document.getElementById('demo-content');

            const summary = data.comprehensive_demo?.demo_summary || {};

            demoContent.innerHTML = `
                <div class="comprehensive-demo-results">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>🏆 Demo Summary</h6>
                            <ul class="list-unstyled">
                                <li><strong>Features Demonstrated:</strong> ${summary.total_features_demonstrated || 6}</li>
                                <li><strong>Success Rate:</strong> ${summary.demo_success_rate || 95}%</li>
                                <li><strong>Overall Score:</strong> ${summary.overall_hackathon_score || 95}/100</li>
                                <li><strong>Winning Potential:</strong> <span class="badge bg-success">VERY HIGH</span></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>🚀 Key Differentiators</h6>
                            <ul class="list-unstyled">
                                ${(summary.key_differentiators || []).map(diff => `<li>• ${diff}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="toggleFullDemoData()">
                            <i class="fas fa-code me-1"></i>View Full Demo Data
                        </button>
                    </div>
                    <div id="full-demo-data" style="display: none;" class="mt-3">
                        <pre class="demo-data">${JSON.stringify(data, null, 2)}</pre>
                    </div>
                </div>
            `;

            demoResults.style.display = 'block';
            demoResults.scrollIntoView({ behavior: 'smooth' });
        }

        function toggleFullDemoData() {
            const fullData = document.getElementById('full-demo-data');
            fullData.style.display = fullData.style.display === 'none' ? 'block' : 'none';
        }

        function showAPIDocumentationModal(data) {
            // Create and show API documentation modal
            const modalHtml = `
                <div class="modal fade" id="apiDocModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-code me-2"></i>TasteShift API Documentation
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="api-doc-content">
                                    <h6>🏆 Winning API - ${data.documentation?.total_endpoints || 25} Endpoints</h6>
                                    <p>${data.documentation?.description || 'Advanced AI-powered cultural intelligence platform'}</p>

                                    <div class="row">
                                        ${Object.entries(data.documentation?.feature_categories || {}).map(([key, category]) => `
                                            <div class="col-md-6 mb-3">
                                                <div class="api-category-card">
                                                    <h6>${category.description}</h6>
                                                    <ul class="list-unstyled">
                                                        ${(category.endpoints || []).map(endpoint => `<li><code>${endpoint}</code></li>`).join('')}
                                                    </ul>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('apiDocModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add new modal to body
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('apiDocModal'));
            modal.show();
        }

        // Auto-start onboarding every time the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we're coming from onboarding (has a parameter)
            const urlParams = new URLSearchParams(window.location.search);
            const fromOnboarding = urlParams.get('from') === 'onboarding';

            // If not coming from onboarding, redirect to onboarding
            if (!fromOnboarding) {
                window.location.href = '/onboarding';
            }
        });
    </script>
</body>
</html>
