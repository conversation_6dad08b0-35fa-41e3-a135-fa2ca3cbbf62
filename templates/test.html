<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Test - TasteShift</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1>🧪 JavaScript Function Test</h1>
                <p class="lead">Testing navigation functions to ensure they work properly</p>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Navigation Function Tests</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Primary Navigation</h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="testFunction('showHome')">
                                        <i class="fas fa-home me-2"></i>Test showHome()
                                    </button>
                                    <button class="btn btn-info" onclick="testFunction('showInsights')">
                                        <i class="fas fa-chart-bar me-2"></i>Test showInsights()
                                    </button>
                                    <button class="btn btn-success" onclick="testFunction('showPersonas')">
                                        <i class="fas fa-users me-2"></i>Test showPersonas()
                                    </button>
                                    <button class="btn btn-warning" onclick="testFunction('showWinningFeatures')">
                                        <i class="fas fa-trophy me-2"></i>Test showWinningFeatures()
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>AI Tools Navigation</h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" onclick="testFunction('showCulturalChat')">
                                        <i class="fas fa-comments me-2"></i>Test showCulturalChat()
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="testFunction('showCrossculturalAdapter')">
                                        <i class="fas fa-exchange-alt me-2"></i>Test showCrossculturalAdapter()
                                    </button>
                                    <button class="btn btn-outline-info" onclick="testFunction('showTrendAnalyzer')">
                                        <i class="fas fa-trending-up me-2"></i>Test showTrendAnalyzer()
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="testFunction('showRiskAssessment')">
                                        <i class="fas fa-exclamation-triangle me-2"></i>Test showRiskAssessment()
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results" class="alert alert-info">
                            Click buttons above to test navigation functions
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="/" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Main App
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    <script>
        function testFunction(functionName) {
            const resultsDiv = document.getElementById('test-results');

            try {
                if (typeof window[functionName] === 'function') {
                    // Capture console logs during function execution
                    const originalLog = console.log;
                    const logs = [];
                    console.log = function(...args) {
                        logs.push(args.join(' '));
                        originalLog.apply(console, args);
                    };

                    window[functionName]();

                    // Restore original console.log
                    console.log = originalLog;

                    resultsDiv.className = 'alert alert-success';
                    resultsDiv.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>SUCCESS:</strong> ${functionName}() executed successfully!
                        ${logs.length > 0 ? `<br><small><strong>Console logs:</strong><br>${logs.join('<br>')}</small>` : ''}
                    `;
                } else {
                    resultsDiv.className = 'alert alert-warning';
                    resultsDiv.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>WARNING:</strong> ${functionName}() is not defined or not a function
                        <br><small>Type: ${typeof window[functionName]}</small>
                    `;
                }
            } catch (error) {
                resultsDiv.className = 'alert alert-danger';
                resultsDiv.innerHTML = `
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>ERROR:</strong> ${functionName}() threw an error: ${error.message}
                    <br><small>Stack: ${error.stack}</small>
                `;
            }
        }
        
        // Log available functions on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 JavaScript Test Page Loaded');
            console.log('Available window functions:', Object.keys(window).filter(key => key.startsWith('show')));
        });
    </script>
</body>
</html>
