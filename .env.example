# TasteShift Environment Variables Template
# Copy this file to .env and fill in your actual values

# Google Gemini API
GEMINI_API_KEY=your_gemini_api_key_here

# Qloo API (for hackathon)
QLOO_API_KEY=your_qloo_api_key_here

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# Database URL (PostgreSQL connection string)
DATABASE_URL=postgresql://username:password@host:port/database

# Session Security
SESSION_SECRET=your_random_secret_key_here

# Optional: Force local database for development
# USE_LOCAL_DB=true

# Optional: Flask environment
# FLASK_ENV=development
