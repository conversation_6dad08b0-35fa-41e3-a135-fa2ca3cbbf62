# Minimal Requirements for TasteShift (avoiding compilation issues)
# Core Flask and Web Framework
Flask>=2.3.0
Flask-SQLAlchemy>=3.0.0
Flask-CORS>=4.0.0
Werkzeug>=2.3.0

# Database Support
psycopg2-binary  # PostgreSQL adapter for Supabase
SQLAlchemy>=2.0.0

# API and HTTP Requests
requests>=2.31.0
urllib3>=2.0.0

# Google AI (Gemini)
google-generativeai>=0.3.0

# Data Processing (use pre-compiled wheels)
pandas>=2.0.0
numpy>=1.24.0

# Environment and Configuration
python-dotenv>=1.0.0

# Essential utilities only
click>=8.1.0
itsdangerous>=2.1.0
Jinja2>=3.1.0
MarkupSafe>=2.1.0
