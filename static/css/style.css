/* TasteShift - Ultra Modern UI with Advanced Gradients & Animations */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

:root {
    /* Ultra Vibrant & Bright Color Palette */
    --primary-gradient: linear-gradient(135deg, #ff6b6b 0%, #ffa726 25%, #42a5f5 50%, #ab47bc 75%, #26c6da 100%);
    --secondary-gradient: linear-gradient(135deg, #ff9800 0%, #ff5722 25%, #e91e63 50%, #9c27b0 75%, #673ab7 100%);
    --accent-gradient: linear-gradient(135deg, #00bcd4 0%, #03a9f4 25%, #2196f3 50%, #3f51b5 75%, #9c27b0 100%);
    --success-gradient: linear-gradient(135deg, #4caf50 0%, #8bc34a 25%, #cddc39 50%, #ffeb3b 75%, #ffc107 100%);
    --warning-gradient: linear-gradient(135deg, #ff9800 0%, #ff5722 25%, #f44336 50%, #e91e63 75%, #9c27b0 100%);
    --danger-gradient: linear-gradient(135deg, #f44336 0%, #e91e63 25%, #9c27b0 50%, #673ab7 75%, #3f51b5 100%);

    /* Ultra Bright Rainbow Gradients */
    --rainbow-gradient: linear-gradient(135deg, #ff6b6b 0%, #ffa726 14%, #ffeb3b 28%, #8bc34a 42%, #26c6da 56%, #42a5f5 70%, #ab47bc 84%, #ff6b6b 100%);
    --coral-gradient: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ffa726 100%);
    --ocean-gradient: linear-gradient(135deg, #00e5ff 0%, #00bcd4 25%, #0097a7 50%, #006064 75%, #004d40 100%);
    --sunset-gradient: linear-gradient(135deg, #ff9a56 0%, #ff6b9d 25%, #c471ed 50%, #12c2e9 75%, #c471ed 100%);
    --forest-gradient: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 25%, #68d391 50%, #48bb78 75%, #38a169 100%);
    --purple-gradient: linear-gradient(135deg, #d53f8c 0%, #b83280 25%, #97266d 50%, #702459 75%, #553c9a 100%);
    --teal-gradient: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 25%, #319795 50%, #2c7a7b 75%, #285e61 100%);
    --pink-gradient: linear-gradient(135deg, #fed7e2 0%, #fbb6ce 25%, #f687b3 50%, #ed64a6 75%, #d53f8c 100%);
    --blue-gradient: linear-gradient(135deg, #90cdf4 0%, #63b3ed 25%, #4299e1 50%, #3182ce 75%, #2b77cb 100%);
    --indigo-gradient: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 25%, #7c3aed 50%, #6d28d9 75%, #5b21b6 100%);
    --emerald-gradient: linear-gradient(135deg, #6ee7b7 0%, #34d399 25%, #10b981 50%, #059669 75%, #047857 100%);
    --tropical-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 25%, #fa709a 50%, #fee140 75%, #fa709a 100%);
    --aurora-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 25%, #d299c2 50%, #fef9d7 75%, #a8edea 100%);

    /* Enhanced Background Gradients */
    --bg-gradient: var(--rainbow-gradient);
    --dark-overlay: linear-gradient(135deg, rgba(15, 23, 42, 0.75) 0%, rgba(30, 41, 59, 0.8) 50%, rgba(15, 23, 42, 0.75) 100%);

    /* Glass Morphism with Enhanced Effects - Light Theme */
    --glass-bg: rgba(255, 255, 255, 0.85);
    --glass-bg-strong: rgba(255, 255, 255, 0.95);
    --glass-bg-ultra: rgba(255, 255, 255, 0.98);
    --glass-border: rgba(148, 163, 184, 0.3);
    --glass-border-strong: rgba(148, 163, 184, 0.5);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --glass-shadow-strong: 0 16px 48px rgba(0, 0, 0, 0.15);

    /* Typography - Light Theme with Dark Text */
    --text-primary: #1e293b !important;
    --text-secondary: #475569 !important;
    --text-muted: #64748b !important;
    --text-accent: #667eea !important;
    --text-bright: #0f172a !important;

    /* Enhanced Shadows & Effects */
    --shadow-soft: 0 4px 20px rgba(102, 126, 234, 0.15);
    --shadow-medium: 0 8px 40px rgba(102, 126, 234, 0.25);
    --shadow-strong: 0 20px 60px rgba(102, 126, 234, 0.4);
    --shadow-glow: 0 0 30px rgba(102, 126, 234, 0.5);
    --shadow-coral: 0 0 30px rgba(255, 107, 107, 0.4);
    --shadow-teal: 0 0 30px rgba(17, 153, 142, 0.4);
    --shadow-pink: 0 0 30px rgba(255, 117, 140, 0.4);

    /* Animations */
    --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Ensure all text has proper contrast */
body, html {
    color: var(--text-primary) !important;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary) !important;
}

p, span, div, label {
    color: var(--text-secondary) !important;
}

.text-primary {
    color: var(--text-primary) !important;
}

.text-secondary {
    color: var(--text-secondary) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 25%, #f1f5f9 50%, #e2e8f0 75%, #cbd5e1 100%);
    min-height: 100vh;
    overflow-x: hidden;
    color: var(--text-primary) !important;
    position: relative;
    padding-top: 80px; /* Add padding to account for fixed navbar */
}

/* Additional padding for main content sections */
#cultural-chat-section,
#personas-section,
#winning-features-section {
    padding-top: 2rem !important;
}

/* Enhanced Light Animated Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background:
        radial-gradient(ellipse at top left, rgba(99, 102, 241, 0.08) 0%, transparent 65%),
        radial-gradient(ellipse at top right, rgba(236, 72, 153, 0.06) 0%, transparent 65%),
        radial-gradient(ellipse at bottom left, rgba(6, 182, 212, 0.08) 0%, transparent 65%),
        radial-gradient(ellipse at bottom right, rgba(245, 158, 11, 0.06) 0%, transparent 65%),
        radial-gradient(ellipse at center, rgba(139, 92, 246, 0.05) 0%, transparent 70%);
    animation: backgroundFlow 25s ease-in-out infinite;
    z-index: -2;
}

/* Floating Particles */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.05) 0%, transparent 50%);
    animation: particleFloat 30s ease-in-out infinite;
    z-index: -1;
}

@keyframes backgroundFlow {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        filter: hue-rotate(0deg) brightness(1);
    }
    25% {
        transform: rotate(90deg) scale(1.05);
        filter: hue-rotate(30deg) brightness(1.1);
    }
    50% {
        transform: rotate(180deg) scale(1.1);
        filter: hue-rotate(60deg) brightness(1.2);
    }
    75% {
        transform: rotate(270deg) scale(1.05);
        filter: hue-rotate(90deg) brightness(1.1);
    }
}
}

/* Ultra Modern Glass Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(30px) saturate(200%);
    border-bottom: 1px solid rgba(148, 163, 184, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    transition: var(--transition-smooth);
    position: relative;
    z-index: 1000;
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--primary-gradient);
    opacity: 0.6;
}

.navbar-brand {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 700;
    font-size: 2rem;
    background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    position: relative;
    transition: var(--transition-smooth);
}

.navbar-brand:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.5));
}

.nav-link {
    color: var(--text-primary) !important;
    font-weight: 500;
    font-size: 0.95rem;
    transition: var(--transition-smooth);
    position: relative;
    padding: 0.75rem 1rem !important;
    border-radius: 12px;
    margin: 0 0.25rem;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    border-radius: 12px;
    opacity: 0;
    transition: var(--transition-smooth);
    z-index: -1;
}

.nav-link:hover {
    color: white !important;
    transform: translateY(-2px);
    background: rgba(99, 102, 241, 0.8) !important;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.nav-link:hover::before {
    opacity: 0.9;
}

/* Enhanced dropdown styling */
.dropdown-toggle::after {
    margin-left: 0.5rem;
    transition: var(--transition-smooth);
}

.dropdown.show .dropdown-toggle::after {
    transform: rotate(180deg);
}

.dropdown:hover .dropdown-menu {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.dropdown-menu {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    display: block;
    visibility: hidden;
}

.dropdown.show .dropdown-menu,
.dropdown:hover .dropdown-menu {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
}

/* Ultra Modern Glass Cards */
.card {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(30px) saturate(200%);
    border: 1px solid rgba(148, 163, 184, 0.2) !important;
    border-radius: 24px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    transition: var(--transition-bounce);
    overflow: hidden;
    position: relative;
    margin-bottom: 2rem;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: var(--transition-smooth);
}

.card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent,
        rgba(102, 126, 234, 0.1),
        transparent,
        rgba(245, 87, 108, 0.1),
        transparent
    );
    animation: cardShimmer 8s linear infinite;
    opacity: 0;
    transition: var(--transition-smooth);
    z-index: -1;
}

.card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
    border-color: rgba(148, 163, 184, 0.3) !important;
    background: rgba(255, 255, 255, 0.98) !important;
}

.card:hover::before {
    opacity: 1;
}

.card:hover::after {
    opacity: 1;
}

@keyframes cardShimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.card-header {
    background: rgba(248, 250, 252, 0.95) !important;
    border-bottom: 1px solid rgba(148, 163, 184, 0.2) !important;
    backdrop-filter: blur(20px) saturate(150%);
    position: relative;
    padding: 1.75rem 2rem;
}

.card-header h3, .card-header h4, .card-header h5 {
    font-family: 'Space Grotesk', sans-serif;
    background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-header h3 {
    font-size: 1.5rem;
}

.card-header h4 {
    font-size: 1.3rem;
}

.card-header h5 {
    font-size: 1.1rem;
}

.card-body {
    padding: 2rem;
    line-height: 1.6;
}

/* Improved spacing for card content */
.card-body > *:not(:last-child) {
    margin-bottom: 1.5rem;
}

.card-body .row {
    margin-bottom: 0;
}

.card-body .row > .col-lg-8,
.card-body .row > .col-lg-4 {
    padding: 0 1rem;
}

/* Next-Gen Buttons */
.btn {
    border-radius: 16px;
    font-weight: 600;
    padding: 14px 28px;
    border: none;
    position: relative;
    overflow: hidden;
    transition: var(--transition-bounce);
    text-transform: none;
    letter-spacing: 0.3px;
    font-size: 0.95rem;
    font-family: 'Inter', sans-serif;
    cursor: pointer;
    z-index: 1;
}

.btn-primary {
    background: var(--ocean-gradient);
    color: white;
    box-shadow: var(--shadow-glow);
}

.btn-success {
    background: var(--teal-gradient);
    color: white;
    box-shadow: var(--shadow-teal);
}

.btn-warning {
    background: var(--coral-gradient);
    color: white;
    box-shadow: var(--shadow-coral);
}

.btn-info {
    background: var(--blue-gradient);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-outline-primary {
    background: transparent;
    border: 2px solid;
    border-image: var(--primary-gradient) 1;
    color: var(--text-accent);
    position: relative;
}

.btn-outline-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    transition: var(--transition-smooth);
    border-radius: 14px;
    z-index: -1;
}

.btn-outline-primary:hover::before {
    opacity: 1;
}

.btn-outline-primary:hover {
    color: white;
    border-color: transparent;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.btn:hover::after {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: var(--shadow-strong);
}

.btn-lg {
    padding: 18px 36px;
    font-size: 1.1rem;
    border-radius: 20px;
}

/* Ultra Modern Form Controls */
.form-select, .form-control {
    background: var(--glass-bg-strong) !important;
    backdrop-filter: blur(25px) saturate(200%);
    border: 1px solid var(--glass-border) !important;
    border-radius: 16px;
    color: var(--text-primary) !important;
    padding: 16px 20px;
    transition: var(--transition-smooth);
    font-size: 0.95rem;
    position: relative;
}

.form-select::before, .form-control::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 16px;
    padding: 1px;
    background: var(--primary-gradient);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: var(--transition-smooth);
}

.form-select:focus, .form-control:focus {
    background: var(--glass-bg-ultra) !important;
    border-color: var(--glass-border-strong) !important;
    box-shadow:
        0 0 0 0.25rem rgba(102, 126, 234, 0.15) !important,
        var(--glass-shadow-strong) !important;
    transform: translateY(-3px) scale(1.02);
    backdrop-filter: blur(30px) saturate(220%);
    outline: none;
}

.form-select:focus::before, .form-control:focus::before {
    opacity: 1;
}

.form-select option {
    background: rgba(255, 255, 255, 0.98) !important;
    color: #1e293b !important;
    padding: 12px;
    font-weight: 500;
}

.form-select option:hover,
.form-select option:focus {
    background: rgba(99, 102, 241, 0.1) !important;
    color: #0f172a !important;
}

.form-select option:checked {
    background: linear-gradient(135deg, #6366f1, #a855f7) !important;
    color: white !important;
}

.form-label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.form-label i {
    margin-right: 0.5rem;
    color: var(--text-accent);
}

/* Ultra Modern Typography */
.display-4 {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 700;
    font-size: 3.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    line-height: 1.2;
    position: relative;
    letter-spacing: -0.02em;
}

.display-4::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 4px;
    background: var(--accent-gradient);
    border-radius: 2px;
}

.lead {
    color: var(--text-secondary);
    font-weight: 400;
    font-size: 1.25rem;
    line-height: 1.7;
    letter-spacing: 0.01em;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: -0.01em;
    line-height: 1.3;
}

h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

h4 {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
}

h5 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

h6 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.text-gradient {
    background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-muted {
    color: var(--text-muted) !important;
    font-weight: 500;
}

/* Enhanced Body Text */
p {
    line-height: 1.7;
    color: var(--text-secondary);
    margin-bottom: 1.25rem;
}

/* Lead text improvements */
.lead {
    color: var(--text-secondary) !important;
    font-weight: 500;
}

/* Card Text Improvements for Light Theme */
.card h3, .card h4 {
    color: var(--text-primary) !important;
}

.card small {
    color: var(--text-muted) !important;
    font-weight: 500;
}

/* Stats Cards Specific Improvements */
.stats-cards .card h4 {
    font-size: 2rem !important;
    font-weight: 800 !important;
    margin-bottom: 0.5rem !important;
}

.stats-cards .card small {
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Improved Text Utilities */
.text-bright {
    color: var(--text-bright) !important;
}

.text-enhanced {
    color: var(--text-primary) !important;
    font-weight: 500;
}

.text-readable {
    line-height: 1.8;
    font-size: 1.05rem;
    color: var(--text-secondary);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    padding: 120px 0 80px;
}

.stats-cards .card {
    background: var(--glass-bg-strong) !important;
    border: 1px solid var(--glass-border) !important;
    transition: var(--transition-smooth);
}

.stats-cards .card:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: var(--shadow-glow);
}

.badge {
    background: var(--glass-bg-strong) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--glass-border);
    padding: 0.75rem 1.25rem;
    border-radius: 25px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: var(--transition-smooth);
}

.badge:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
}

/* Score Display with Advanced Gradient */
.score-display {
    background: var(--glass-bg-strong);
    backdrop-filter: blur(25px) saturate(180%);
    border: 1px solid var(--glass-border);
    border-radius: 24px;
    padding: 3rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.score-display::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        rgba(102, 126, 234, 0.3),
        rgba(245, 87, 108, 0.3),
        rgba(79, 172, 254, 0.3),
        rgba(240, 147, 251, 0.3),
        rgba(102, 126, 234, 0.3)
    );
    animation: scoreRotate 15s linear infinite;
    z-index: -1;
}

.score-display::after {
    content: '';
    position: absolute;
    inset: 3px;
    background: rgba(12, 12, 12, 0.8);
    border-radius: 21px;
    z-index: -1;
    backdrop-filter: blur(10px);
}

@keyframes scoreRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.score-number {
    font-size: 4rem;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    position: relative;
    z-index: 1;
}

/* Advanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.95);
        filter: blur(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-60px) rotate(-5deg);
    }
    to {
        opacity: 1;
        transform: translateX(0) rotate(0deg);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(60px) rotate(5deg);
    }
    to {
        opacity: 1;
        transform: translateX(0) rotate(0deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 20px rgba(102, 126, 234, 0);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes glow {
    0%, 100% { filter: drop-shadow(0 0 5px rgba(102, 126, 234, 0.5)); }
    50% { filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.8)); }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeInUp 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-fade-out {
    animation: fadeOut 0.3s ease-in forwards;
}

.animate-slide-in {
    animation: slideInLeft 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-slide-in-right {
    animation: slideInRight 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-slide-up {
    animation: slideUp 0.6s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.5s ease-out forwards;
}

.animate-pulse {
    animation: pulse 3s ease-in-out infinite;
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-glow {
    animation: glow 4s ease-in-out infinite;
}

/* Enhanced Persona Display Styling */
.persona-results-container {
    animation: slideUp 0.8s ease-out forwards;
}

.persona-header-card {
    background: var(--glass-bg-ultra) !important;
    border: 2px solid transparent !important;
    background-image: linear-gradient(var(--glass-bg-ultra), var(--glass-bg-ultra)), var(--primary-gradient) !important;
    background-origin: border-box !important;
    background-clip: content-box, border-box !important;
    box-shadow: var(--glass-shadow-strong);
}

.persona-header {
    background: transparent !important;
    border: none !important;
    padding: 2rem 2.5rem;
}

.persona-title {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 700;
    font-size: 1.75rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.persona-meta {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-top: 0.5rem;
}

.persona-meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.persona-meta-item i {
    color: var(--text-accent);
}

.persona-actions .btn {
    border-radius: 12px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition-smooth);
}

.persona-content-row {
    gap: 2rem;
}

.persona-main-content {
    height: 100%;
}

.persona-content-area {
    background: var(--glass-bg-strong);
    border-radius: 24px;
    padding: 2.5rem;
    border-left: 6px solid;
    border-image: var(--primary-gradient) 1;
    backdrop-filter: blur(20px) saturate(180%);
    position: relative;
    overflow: hidden;
    font-size: 1.05rem;
    line-height: 1.8;
    letter-spacing: 0.01em;
    box-shadow: var(--glass-shadow);
    transition: var(--transition-smooth);
}

.persona-content-area:hover {
    transform: translateY(-5px);
    box-shadow: var(--glass-shadow-strong);
}

.persona-content-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    opacity: 0.8;
}

.persona-insights-panel {
    height: 100%;
}

.persona-insights-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.persona-insights-card .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chart-section {
    flex: 0 0 auto;
}

.attributes-section {
    flex: 1;
    min-height: 0;
}

.insights-title {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 600;
    background: var(--indigo-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.attributes-title {
    font-weight: 600;
    font-size: 1rem;
}

.attributes-list {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.attributes-list::-webkit-scrollbar {
    width: 4px;
}

.attributes-list::-webkit-scrollbar-track {
    background: var(--glass-bg);
    border-radius: 2px;
}

.attributes-list::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* Enhanced Persona Content Styling */
.persona-section {
    margin-bottom: 2.5rem;
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--glass-border);
    transition: var(--transition-smooth);
}

.persona-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.persona-section:hover {
    background: var(--glass-bg);
    border-radius: 16px;
    padding: 1.5rem;
    margin: 0 -1.5rem 2.5rem -1.5rem;
}

.persona-section-title {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 600;
    font-size: 1.3rem;
    color: var(--text-bright);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.persona-section-title i {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.1rem;
}

.persona-section-content {
    color: var(--text-secondary);
    line-height: 1.8;
    font-size: 1.05rem;
}

.persona-section-content p {
    margin-bottom: 1.25rem;
}

.persona-section-content strong {
    color: var(--text-primary);
    font-weight: 600;
}

.persona-section-content em {
    color: var(--text-accent);
    font-style: normal;
    font-weight: 500;
}

.persona-highlight {
    background: linear-gradient(135deg, var(--glass-bg-strong), var(--glass-bg-ultra));
    border-radius: 12px;
    padding: 1.25rem;
    margin: 1.5rem 0;
    border-left: 4px solid;
    border-image: var(--accent-gradient) 1;
    backdrop-filter: blur(10px);
}

.persona-highlight .highlight-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.persona-highlight .highlight-content {
    color: var(--text-secondary);
    line-height: 1.7;
}

.persona-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1.5rem;
}

.persona-tag {
    background: var(--glass-bg-strong);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
    transition: var(--transition-smooth);
}

.persona-tag:hover {
    background: var(--glass-bg-ultra);
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
}

.persona-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.persona-stat {
    background: var(--glass-bg);
    border-radius: 16px;
    padding: 1.25rem;
    text-align: center;
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
    transition: var(--transition-smooth);
}

.persona-stat:hover {
    background: var(--glass-bg-strong);
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.persona-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.persona-stat-label {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Toast Notification System */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--glass-bg-strong);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 1rem 1.5rem;
    box-shadow: var(--glass-shadow);
    z-index: 9999;
    min-width: 300px;
    max-width: 400px;
}

.toast-success {
    border-left: 4px solid #10b981;
}

.toast-error {
    border-left: 4px solid #ef4444;
}

.toast-warning {
    border-left: 4px solid #f59e0b;
}

.toast-info {
    border-left: 4px solid #3b82f6;
}

.toast-content {
    display: flex;
    align-items: center;
    color: var(--text-primary);
    font-weight: 500;
}

.toast-success .toast-content i {
    color: #10b981;
}

.toast-error .toast-content i {
    color: #ef4444;
}

.toast-warning .toast-content i {
    color: #f59e0b;
}

.toast-info .toast-content i {
    color: #3b82f6;
}

/* Enhanced Mobile Responsiveness for Persona Display */
@media (max-width: 992px) {
    .persona-content-row {
        flex-direction: column;
        gap: 1.5rem;
    }

    .persona-insights-panel {
        order: -1;
    }

    .persona-insights-card .card-body {
        flex-direction: row;
        gap: 2rem;
    }

    .chart-section {
        flex: 0 0 300px;
    }

    .attributes-section {
        flex: 1;
        margin-top: 0;
    }
}

@media (max-width: 768px) {
    .persona-header {
        padding: 1.5rem;
    }

    .persona-title {
        font-size: 1.5rem;
    }

    .persona-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .persona-actions {
        margin-top: 1rem;
    }

    .persona-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .persona-content-area {
        padding: 1.5rem;
        border-left-width: 4px;
    }

    .persona-insights-card .card-body {
        flex-direction: column;
        gap: 1.5rem;
    }

    .chart-section {
        flex: none;
    }

    .chart-container {
        height: 220px !important;
    }

    .persona-section {
        margin-bottom: 2rem;
        padding: 1rem 0;
    }

    .persona-section:hover {
        padding: 1rem;
        margin: 0 -1rem 2rem -1rem;
    }

    .persona-section-title {
        font-size: 1.2rem;
    }

    .persona-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .persona-stat {
        padding: 1rem;
    }

    .persona-stat-value {
        font-size: 1.3rem;
    }

    .toast-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        min-width: auto;
        max-width: none;
    }
}

@media (max-width: 576px) {
    .persona-header-card {
        margin: 0 -0.5rem;
        border-radius: 20px !important;
    }

    .persona-title {
        font-size: 1.3rem;
    }

    .persona-content-area {
        padding: 1.25rem;
        font-size: 1rem;
    }

    .persona-section-title {
        font-size: 1.1rem;
        gap: 0.5rem;
    }

    .persona-section-content {
        font-size: 1rem;
    }

    .persona-stats {
        grid-template-columns: 1fr;
    }

    .persona-tags {
        gap: 0.5rem;
    }

    .persona-tag {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }

    .attributes-list {
        max-height: 250px;
    }

    .persona-attribute {
        padding: 1rem 0;
    }

    .persona-attribute:hover {
        padding: 1rem;
        margin: 0 -1rem;
    }

    .attribute-label {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .attribute-value {
        font-size: 1rem;
        min-width: 40px;
    }

    .attribute-bar {
        height: 10px;
    }
}

.persona-description::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-gradient);
}

/* Advanced Loading States */
.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-top: 4px solid transparent;
    border-radius: 50%;
    animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    position: relative;
}

.loading-spinner::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border: 2px solid transparent;
    border-top: 2px solid rgba(102, 126, 234, 0.8);
    border-radius: 50%;
    animation: spin 0.8s linear infinite reverse;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.progress {
    background: var(--glass-bg) !important;
    border-radius: 10px !important;
    overflow: hidden;
    height: 8px !important;
}

.progress-bar {
    background: var(--primary-gradient) !important;
    transition: width 0.6s ease !important;
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Creative Suggestions Grid */
.suggestions-grid .card {
    height: 100%;
    transition: var(--transition-bounce);
    cursor: pointer;
    position: relative;
    min-height: 280px;
}

.suggestions-grid .card:hover {
    transform: translateY(-8px) rotate(1deg) scale(1.02);
    box-shadow: var(--shadow-strong);
}

.suggestions-grid .card-title {
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.suggestion-card .card-body {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.suggestion-card .card-text {
    font-size: 0.95rem;
    line-height: 1.6;
    color: var(--text-secondary);
    flex-grow: 1;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
}

.suggestion-card .card-text br {
    margin-bottom: 0.5rem;
}

/* Enhanced Status Indicators - Light Theme */
.status-success {
    color: #059669;
    text-shadow: 0 0 15px rgba(5, 150, 105, 0.3);
    font-weight: 700;
}

.status-warning {
    color: #d97706;
    text-shadow: 0 0 15px rgba(217, 119, 6, 0.3);
    font-weight: 700;
}

.status-danger {
    color: #dc2626;
    text-shadow: 0 0 15px rgba(220, 38, 38, 0.3);
    font-weight: 700;
}

/* Feature Icons */
.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: iconShimmer 3s linear infinite;
}

.feature-icon:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-glow);
}

@keyframes iconShimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Onboarding Styles */
.onboarding-hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    padding: 2rem 0;
}

.onboarding-card {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    position: relative;
}

.onboarding-step {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out forwards;
}

.step-indicator {
    display: flex;
    justify-content: center;
    margin: 3rem 0 2rem;
    gap: 1rem;
}

.step {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transition: var(--transition-smooth);
    position: relative;
    cursor: pointer;
}

.step::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: var(--transition-smooth);
}

.step.active {
    background: var(--primary-gradient);
    transform: scale(1.3);
    box-shadow: var(--shadow-glow);
}

.step.active::before {
    background: white;
    width: 6px;
    height: 6px;
}

/* Modal Enhancements */
.modal-content {
    background: var(--glass-bg-strong) !important;
    backdrop-filter: blur(25px) saturate(180%);
    border: 1px solid var(--glass-border) !important;
    border-radius: 24px !important;
    box-shadow: var(--shadow-strong);
}

.modal-header {
    border-bottom: 1px solid var(--glass-border) !important;
}

.modal-body {
    padding: 2.5rem;
}

/* Utility Classes */
.text-glow {
    text-shadow: 0 0 20px currentColor;
}

.bg-gradient-primary {
    background: var(--primary-gradient) !important;
}

.bg-gradient-secondary {
    background: var(--secondary-gradient) !important;
}

.bg-gradient-success {
    background: var(--success-gradient) !important;
}

.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(var(--bg-gradient), var(--bg-gradient)) padding-box,
                var(--primary-gradient) border-box;
    border-radius: 16px;
}

/* Enhanced Persona Attributes Styling */
.persona-attribute {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem 0;
    border-bottom: 1px solid var(--glass-border);
    transition: var(--transition-smooth);
    border-radius: 12px;
    opacity: 0;
    transform: translateY(20px);
}

.persona-attribute:last-child {
    border-bottom: none;
}

.persona-attribute:hover {
    background: var(--glass-bg);
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    margin: 0 -1.25rem;
    transform: translateX(8px) translateY(0);
    box-shadow: var(--shadow-soft);
}

.attribute-info {
    flex: 1;
    margin-right: 1rem;
}

.attribute-label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.attribute-label i {
    color: var(--text-accent);
    font-size: 0.9rem;
}

.attribute-value {
    font-weight: 700;
    color: var(--text-primary);
    font-size: 1.1rem;
    min-width: 50px;
    text-align: right;
    font-family: 'Space Grotesk', sans-serif;
}

.value-number {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.attribute-bar {
    width: 100%;
    height: 12px;
    background: var(--glass-bg);
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.attribute-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.15) 100%);
    border-radius: 8px;
}

.attribute-fill {
    height: 100%;
    border-radius: 8px;
    position: relative;
    z-index: 1;
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

.attribute-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    border-radius: 8px;
    opacity: 0;
    animation: attributeGlow 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes attributeGlow {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Score Interpretation Styling */
.score-low {
    background: var(--success-gradient) !important;
    color: white !important;
    border: none !important;
}

.score-medium {
    background: var(--warning-gradient) !important;
    color: white !important;
    border: none !important;
}

.score-high {
    background: var(--danger-gradient) !important;
    color: white !important;
    border: none !important;
}

/* Landscape Persona Layout Styles */
.persona-landscape-container {
    width: 100%;
    max-width: 100%;
}

.persona-main-content-landscape {
    background: var(--glass-bg-strong);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-lg);
    margin-bottom: 2rem;
}

.persona-content-area-landscape {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    width: 100%;
}

.persona-section {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border-strong);
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    backdrop-filter: blur(15px);
    transition: var(--transition-smooth);
    height: fit-content;
}

.persona-section:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-strong);
    background: var(--glass-bg-strong);
}

.persona-insights-row {
    gap: 1.5rem;
}

.persona-insights-card,
.persona-attributes-card {
    background: var(--glass-bg-strong);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-smooth);
}

.persona-insights-card:hover,
.persona-attributes-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-strong);
}

.attributes-list-landscape {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    max-height: 300px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.attributes-list-landscape::-webkit-scrollbar {
    width: 6px;
}

.attributes-list-landscape::-webkit-scrollbar-track {
    background: var(--glass-bg);
    border-radius: 3px;
}

.attributes-list-landscape::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 3px;
}

/* Chart Container Enhancements */
.chart-container {
    position: relative;
    height: 100%;
    width: 100%;
    border-radius: 16px;
    overflow: hidden;
}

/* Chart Error Handling */
.chart-error-message {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    backdrop-filter: blur(10px);
    z-index: 10;
}

.chart-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--glass-bg-strong);
    border-radius: var(--border-radius-md);
    backdrop-filter: blur(15px);
    z-index: 20;
}

.chart-loading-content {
    text-align: center;
    color: var(--text-primary);
}

.chart-spinner {
    font-size: 2rem;
    color: var(--primary-gradient);
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chart-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;
    z-index: 10;
}

.chart-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.chart-subtitle {
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* Enhanced Chart Loading States */
.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-muted);
    font-size: 0.9rem;
    z-index: 20;
    text-align: center;
}

.chart-loading i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
    color: var(--text-accent);
    animation: chartLoadingSpin 1.5s linear infinite;
}

.chart-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 30;
    border-radius: 12px;
}

.chart-loading-content {
    text-align: center;
    color: var(--text-secondary);
}

.chart-spinner {
    font-size: 2rem;
    color: var(--text-accent);
    margin-bottom: 1rem;
}

.chart-spinner i {
    animation: chartLoadingSpin 2s linear infinite;
}

/* Enhanced Loading Animations */
.loading-spinner {
    display: inline-block;
    position: relative;
    width: 64px;
    height: 64px;
}

.spinner-ring {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 51px;
    height: 51px;
    margin: 6px;
    border: 6px solid var(--text-accent);
    border-radius: 50%;
    animation: spinnerRing 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: var(--text-accent) transparent transparent transparent;
}

.spinner-ring:nth-child(1) { animation-delay: -0.45s; }
.spinner-ring:nth-child(2) { animation-delay: -0.3s; }
.spinner-ring:nth-child(3) { animation-delay: -0.15s; }

.loading-text {
    font-size: 1.1rem;
    color: #333333 !important;
    margin-top: 1rem;
    text-align: center;
    font-weight: 600;
    opacity: 1 !important;
    visibility: visible !important;
}

@keyframes chartLoadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes spinnerRing {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Enhanced Canvas Styling */
canvas {
    border-radius: 12px;
    transition: var(--transition-smooth);
}

canvas:hover {
    transform: scale(1.02);
}

/* Chart Card Specific Styling */
.card-body[style*="height"] {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* Persona Insights Card Enhancement */
.border-gradient {
    border: 2px solid transparent !important;
    background: linear-gradient(var(--glass-bg-strong), var(--glass-bg-strong)) padding-box,
                var(--primary-gradient) border-box;
    border-radius: 20px !important;
    backdrop-filter: blur(25px) saturate(180%);
    box-shadow: var(--glass-shadow);
}

.border-gradient:hover {
    background: linear-gradient(var(--glass-bg-ultra), var(--glass-bg-ultra)) padding-box,
                var(--primary-gradient) border-box;
    box-shadow: var(--glass-shadow-strong);
}

/* Infographic Elements */
.infographic-item {
    background: var(--glass-bg-strong);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.infographic-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-gradient);
}

.infographic-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.infographic-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.infographic-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.infographic-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Enhanced Persona Cards */
.hover-lift {
    transition: all 0.3s ease;
    cursor: pointer;
}

.hover-lift:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.persona-card .card {
    border: none;
    border-radius: 12px;
    overflow: hidden;
}

.persona-card .card-header {
    border-bottom: none;
    padding: 1rem;
}

.persona-card .card-body {
    padding: 1.25rem;
}

.persona-card .card-footer {
    border-top: 1px solid rgba(0,0,0,0.1);
    padding: 0.75rem 1.25rem;
}

.persona-card .btn-group .btn {
    border-radius: 6px;
    margin: 0 2px;
    flex: 1;
}

.persona-card .btn-group .btn:hover {
    transform: translateY(-1px);
}

/* Campaign Analysis Modal */
#campaignAnalysisModal .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
}

#campaignAnalysisModal .modal-header {
    border-radius: 15px 15px 0 0;
    border-bottom: none;
    padding: 1.5rem;
}

#campaignAnalysisModal .modal-body {
    padding: 2rem;
}

#campaignAnalysisModal .card {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Filter Controls */
.input-group .form-control,
.form-select {
    border-radius: 8px;
    border: 1px solid #e0e6ed;
    transition: all 0.3s ease;
}

.input-group .form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    border: 1px solid #e0e6ed;
    background: #f8f9fa;
}

/* Empty State */
#personas-empty-state {
    padding: 4rem 2rem;
}

#personas-empty-state i {
    opacity: 0.5;
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .display-4 {
        font-size: 3rem;
    }

    .hero-section {
        padding: 100px 0 60px;
    }
}

@media (max-width: 992px) {
    .display-4 {
        font-size: 2.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    body {
        padding-top: 70px; /* Reduced padding for mobile navbar */
    }

    .display-4 {
        font-size: 2rem;
        line-height: 1.3;
    }

    .lead {
        font-size: 1.1rem;
    }

    .card {
        margin: 0.5rem;
        border-radius: 20px !important;
    }

    .card-body {
        padding: 1.25rem;
    }

    .card-header {
        padding: 1.25rem 1.5rem;
    }

    .btn {
        padding: 12px 24px;
        font-size: 0.9rem;
    }

    .btn-lg {
        padding: 16px 32px;
        font-size: 1rem;
    }

    .navbar-brand {
        font-size: 1.5rem;
    }

    .hero-section {
        padding: 80px 0 40px;
        text-align: center;
    }

    .onboarding-card {
        max-width: 100%;
        padding: 0 1rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .score-display {
        padding: 2rem;
    }

    .score-number {
        font-size: 3rem;
    }

    /* Mobile Chart Responsiveness */
    .chart-container {
        height: 250px !important;
    }

    .card-body[style*="height"] {
        height: 250px !important;
    }

    /* Mobile Persona Cards */
    .persona-card .btn-group .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }

    .persona-card .btn-group .btn i {
        margin: 0;
    }

    #campaignAnalysisModal .modal-body {
        padding: 1rem;
    }

    .persona-attribute {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        padding: 0.75rem 0;
    }

    .persona-attribute:hover {
        margin: 0;
        padding: 0.75rem;
    }

    .attribute-bar {
        width: 100%;
        margin-top: 0.5rem;
    }

    /* Mobile Navigation */
    .navbar-nav {
        text-align: center;
        padding: 1rem 0;
    }

    .nav-link {
        margin: 0.25rem 0;
    }

    /* Mobile Form Improvements */
    .form-select, .form-control {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Mobile Glass Effects */
    .card {
        backdrop-filter: blur(20px) saturate(150%);
    }

    .navbar {
        backdrop-filter: blur(20px) saturate(150%);
    }
}

@media (max-width: 576px) {
    .display-4 {
        font-size: 1.8rem;
    }

    .container {
        padding: 0 1rem;
    }

    .card-header {
        padding: 1rem;
    }

    .card-header h3 {
        font-size: 1.3rem;
    }

    .form-select, .form-control {
        padding: 14px 16px;
        font-size: 0.9rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .step-indicator {
        margin: 2rem 0 1rem;
    }

    .step {
        width: 12px;
        height: 12px;
    }

    /* Mobile Landscape Layout Responsiveness */
    .persona-content-area-landscape {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .persona-main-content-landscape {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .persona-insights-row {
        flex-direction: column;
        gap: 1rem;
    }

    .persona-insights-row .col-lg-6 {
        width: 100%;
        margin-bottom: 1rem;
    }

    /* Mobile Chart Responsiveness */
    .chart-container {
        height: 250px !important;
    }

    .card-body[style*="height"] {
        height: 250px !important;
    }

    .persona-attribute {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .attribute-value {
        align-self: flex-end;
    }

    .attributes-list-landscape {
        max-height: 200px;
    }

    .infographic-item {
        padding: 1rem;
    }

    .infographic-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    /* Dashboard Stats Mobile */
    .stats-cards .card {
        margin-bottom: 1rem;
    }

    .feature-icon {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.2rem !important;
    }

    /* Export buttons mobile */
    .d-flex.flex-wrap.gap-3 {
        flex-direction: column !important;
        gap: 0.5rem !important;
    }

    .d-flex.flex-wrap.gap-3 .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: rgba(255, 255, 255, 0.98);
        --text-secondary: rgba(255, 255, 255, 0.8);
        --text-muted: rgba(255, 255, 255, 0.6);
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .animate-pulse,
    .animate-float,
    .animate-glow {
        animation: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --glass-bg: rgba(255, 255, 255, 0.15);
        --glass-border: rgba(255, 255, 255, 0.3);
        --text-primary: #ffffff;
        --text-secondary: #e0e0e0;
    }

    .card {
        border-width: 2px !important;
    }

    .btn {
        border: 2px solid currentColor;
    }
}

/* ===== RISK ASSESSMENT STYLES ===== */

/* Risk Assessment Input Modal */
.risk-input-modal .modal-content {
    background: var(--glass-bg-strong);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(30px) saturate(200%);
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.risk-input-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 2rem;
    position: relative;
}

.risk-input-modal .modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    pointer-events: none;
}

.risk-input-modal .modal-title {
    font-size: 1.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.risk-input-modal .modal-body {
    padding: 2.5rem;
    background: rgba(255, 255, 255, 0.98);
}

.risk-form-group {
    margin-bottom: 2rem;
    position: relative;
}

.risk-form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.risk-form-control {
    border: 2px solid rgba(148, 163, 184, 0.2);
    border-radius: 16px;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.risk-form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

.risk-form-control::placeholder {
    color: #94a3b8;
    font-style: italic;
}

.risk-concerns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.risk-concern-item {
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: 16px;
    padding: 1.25rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.risk-concern-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.risk-concern-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    border-color: #667eea;
}

.risk-concern-item.selected {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.risk-concern-item.selected::before {
    opacity: 0.1;
}

.risk-concern-content {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.risk-concern-icon {
    width: 24px;
    height: 24px;
    color: #667eea;
    transition: color 0.3s ease;
}

.risk-concern-item.selected .risk-concern-icon {
    color: #667eea;
}

.risk-concern-label {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    transition: color 0.3s ease;
}

.risk-concern-item.selected .risk-concern-label {
    color: #667eea;
}

.risk-form-actions {
    padding: 2rem;
    background: rgba(248, 250, 252, 0.8);
    border-top: 1px solid var(--glass-border);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.risk-btn-analyze {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 16px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.risk-btn-analyze::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.risk-btn-analyze:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.risk-btn-analyze:hover::before {
    left: 100%;
}

/* Risk Assessment Results Modal */
.risk-results-modal .modal-content {
    background: var(--glass-bg-strong);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(30px) saturate(200%);
    border-radius: 24px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.risk-results-modal .modal-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: none;
    padding: 2rem;
    position: relative;
}

.risk-results-modal .modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    pointer-events: none;
}

.risk-level-card {
    background: var(--glass-bg-strong);
    border: 2px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
}

.risk-level-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: conic-gradient(from 0deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    animation: rotate 3s linear infinite;
    z-index: -1;
}

.risk-level-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.risk-level-indicator {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.risk-level-low {
    color: #10b981;
    border-color: rgba(16, 185, 129, 0.3);
}

.risk-level-medium {
    color: #f59e0b;
    border-color: rgba(245, 158, 11, 0.3);
}

.risk-level-high {
    color: #ef4444;
    border-color: rgba(239, 68, 68, 0.3);
}

.risk-score-progress {
    position: relative;
    height: 12px;
    background: rgba(148, 163, 184, 0.2);
    border-radius: 6px;
    overflow: hidden;
    margin: 1rem 0;
}

.risk-score-bar {
    height: 100%;
    border-radius: 6px;
    position: relative;
    transition: width 1s ease-in-out;
}

.risk-score-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

.risk-score-low {
    background: linear-gradient(90deg, #10b981, #34d399);
}

.risk-score-medium {
    background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.risk-score-high {
    background: linear-gradient(90deg, #ef4444, #f87171);
}

.risk-item-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.risk-item-card::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
}

.risk-item-card:hover {
    transform: translateX(8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.risk-item-card:hover::before {
    width: 8px;
}

.risk-category-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.risk-category-cultural {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.risk-category-religious {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.risk-category-political {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.risk-category-social {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.risk-description {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
}

.risk-markets {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-style: italic;
}

.recommendations-section {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(15px);
}

.recommendation-item {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.recommendation-item::before {
    content: '✓';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #10b981, #34d399);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: bold;
    transition: all 0.3s ease;
}

.recommendation-item:hover {
    transform: translateX(10px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.recommendation-item:hover::before {
    transform: translateY(-50%) scale(1.1);
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== CAMPAIGN ADAPTER STYLES ===== */

.campaign-adapter-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    padding: 5rem 0 3rem 0; /* Increased top padding for navbar */
    position: relative;
}

.campaign-adapter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23667eea' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    pointer-events: none;
}

/* Header Styles */
.adapter-header-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    animation: float 3s ease-in-out infinite;
}

.adapter-header-icon i {
    font-size: 2.5rem;
    color: white;
}

.adapter-main-title {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.adapter-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    font-weight: 500;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Main Card Styles */
.adapter-main-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(148, 163, 184, 0.1);
    border-radius: 24px;
    backdrop-filter: blur(20px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.adapter-main-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 35px 70px rgba(0, 0, 0, 0.12);
}

.adapter-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.adapter-card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: shimmer 3s infinite;
}

.adapter-header-icon-small {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.adapter-header-icon-small i {
    font-size: 1.5rem;
    color: white;
}

.adapter-card-header h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0;
}

.adapter-card-body {
    padding: 3rem;
}

/* Section Styles */
.adapter-section {
    position: relative;
}

.adapter-form-group {
    margin-bottom: 1.5rem;
}

.adapter-form-label {
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    font-size: 1.1rem;
}

.adapter-form-label i {
    color: #667eea;
    margin-right: 0.5rem;
}

.adapter-form-control {
    width: 100%;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 1.25rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #ffffff;
    color: #1e293b;
    font-family: inherit;
}

.adapter-form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.adapter-form-control::placeholder {
    color: #94a3b8;
    font-style: italic;
}

/* Region Card Styles */
.adapter-region-card {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.adapter-region-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.1);
}

.adapter-region-header {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    padding: 1rem 1.5rem;
    font-weight: 700;
    color: #1e293b;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
}

.adapter-region-header i {
    color: #667eea;
    font-size: 1.1rem;
}

.adapter-region-body {
    padding: 1.5rem;
}

.adapter-region-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 1rem center;
    background-repeat: no-repeat;
    background-size: 1.25em 1.25em;
    padding-right: 3rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
}

.adapter-region-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.adapter-help-text {
    margin-top: 0.75rem;
    font-size: 0.875rem;
    color: #64748b;
    font-style: italic;
    display: flex;
    align-items: center;
}

.adapter-help-text i {
    color: #667eea;
    margin-right: 0.25rem;
}

/* Enhanced Generate Button */
.adapter-btn-generate {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 1.5rem 3rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    cursor: pointer;
    min-width: 300px;
    justify-content: center;
}

.adapter-btn-generate:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.adapter-btn-generate .btn-icon {
    font-size: 1.25rem;
    transition: transform 0.3s ease;
}

.adapter-btn-generate:hover .btn-icon {
    transform: rotate(360deg);
}

.adapter-btn-generate .btn-text {
    font-weight: 700;
    letter-spacing: 0.5px;
}

.adapter-btn-generate .btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.adapter-btn-generate:hover .btn-shine {
    left: 100%;
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Results Section */
.adaptation-results-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(148, 163, 184, 0.1);
    border-radius: 24px;
    backdrop-filter: blur(20px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.08);
    min-height: 400px;
    position: relative;
    overflow: hidden;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .adapter-main-title {
        font-size: 2.25rem;
    }

    .adapter-subtitle {
        font-size: 1.1rem;
    }

    .adapter-card-body {
        padding: 2rem 1.5rem;
    }

    .adapter-btn-generate {
        min-width: 250px;
        padding: 1.25rem 2rem;
        font-size: 1rem;
    }

    .adapter-region-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .campaign-adapter-section {
        padding: 4rem 0 2rem 0; /* Maintain navbar clearance on mobile */
    }

    .adapter-main-title {
        font-size: 1.875rem;
    }

    .adapter-card-body {
        padding: 1.5rem 1rem;
    }

    .adapter-btn-generate {
        min-width: 200px;
        padding: 1rem 1.5rem;
    }
}

.adaptation-item {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.adaptation-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

/* Adaptation Results Styling */
.adaptation-region-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.875rem;
    display: inline-block;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.adaptation-content {
    color: #1e293b;
    line-height: 1.6;
}

.adaptation-content h6 {
    color: #667eea;
    font-weight: 700;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.adaptation-content h6:first-child {
    margin-top: 0;
}

.adaptation-content p {
    margin-bottom: 0.75rem;
    color: #475569;
}

.adaptation-content ul {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.adaptation-content li {
    margin-bottom: 0.5rem;
    color: #475569;
    line-height: 1.5;
}

.adaptation-content strong {
    color: #1e293b;
    font-weight: 600;
}

/* Loading Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.adaptation-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 2px;
}

.adaptation-item:hover {
    transform: translateX(8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.adaptation-region-badge {
    display: inline-block;
    padding: 0.5rem 1.25rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.adaptation-content {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-primary);
}

/* ===== TREND ANALYZER STYLES ===== */

.trend-analyzer-section {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(59, 130, 246, 0.05));
    min-height: 100vh;
    padding: 4rem 0 2rem 0; /* Increased top padding for navbar */
}

.trend-settings-card {
    background: var(--glass-bg-strong);
    border: 2px solid var(--glass-border);
    border-radius: 24px;
    backdrop-filter: blur(30px) saturate(200%);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.trend-settings-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6);
    animation: shimmer 3s infinite;
}

.trend-category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

/* Enhanced landscape layout styling for trend analyzer */
.trend-settings-card .row .col-md-6 .adapter-form-group {
    margin-bottom: 1.5rem;
}

.trend-settings-card .text-center {
    margin-top: 1rem;
}

.trend-category-item {
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: 16px;
    padding: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    text-align: center;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.trend-category-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #10b981, #3b82f6);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 14px;
    z-index: 0;
}

.trend-category-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
}

.trend-category-item.selected {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.25);
}

.trend-category-item.selected::before {
    opacity: 0.1;
}

.trend-category-content {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.trend-category-icon {
    font-size: 1.5rem;
    color: #10b981;
    transition: color 0.3s ease;
}

.trend-category-label {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    font-size: 0.9rem;
}

.trend-btn-analyze {
    background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%);
    border: none;
    color: white;
    padding: 1.25rem 3rem;
    border-radius: 16px;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    max-width: 400px;
    margin: 0 auto;
    display: inline-block;
}

.trend-btn-analyze::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.trend-btn-analyze:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(16, 185, 129, 0.4);
}

.trend-btn-analyze:hover::before {
    left: 100%;
}

.trend-results-card {
    background: var(--glass-bg-strong);
    border: 2px solid var(--glass-border);
    border-radius: 24px;
    backdrop-filter: blur(30px) saturate(200%);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    min-height: 500px;
}

.trend-item {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.trend-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #10b981, #3b82f6);
    border-radius: 2px;
}

.trend-item:hover {
    transform: translateX(8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.trend-badge {
    display: inline-block;
    padding: 0.4rem 1rem;
    background: linear-gradient(135deg, #10b981, #3b82f6);
    color: white;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.trend-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
}

.trend-description {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
}

.trend-impact {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-style: italic;
}

/* ===== BUSINESS VALUE CALCULATOR STYLES ===== */

.business-value-section {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    min-height: 100vh;
    padding: 4rem 0 2rem 0; /* Increased top padding for navbar */
}

.roi-metric-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.roi-metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.roi-metric-value {
    font-size: 2rem;
    font-weight: 800;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.roi-metric-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.case-study-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    height: 100%;
}

.case-study-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.case-study-company {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.case-study-industry {
    font-size: 0.85rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
}

.case-study-challenge {
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.expansion-opportunity {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    border-left: 4px solid #10b981;
}

.expansion-opportunity:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.expansion-market {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.expansion-potential {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.expansion-value {
    font-size: 1rem;
    font-weight: 600;
    color: #10b981;
}

/* ===== INDUSTRY INSIGHTS SECTION ===== */
.insight-metric {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.insight-metric:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.insight-value {
    font-size: 1.8rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.insight-label {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#industry-insights-section .card-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
    border-radius: 12px 12px 0 0;
}

#industry-insights-section .card {
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    overflow: hidden;
}

#industry-insights-section .list-unstyled li {
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    transition: all 0.3s ease;
}

#industry-insights-section .list-unstyled li:last-child {
    border-bottom: none;
}

#industry-insights-section .list-unstyled li:hover {
    background: rgba(102, 126, 234, 0.05);
    padding-left: 0.5rem;
    border-radius: 6px;
}

#industry-insights-section .fas {
    width: 20px;
    text-align: center;
    margin-right: 0.5rem;
}

#industry-insights-section h6 {
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* Interactive Persona Visualizations */
.persona-chart-container {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.persona-chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.metric-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    padding: 1.25rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== CULTURAL INTELLIGENCE CHAT STYLES ===== */

.chat-container {
    background: var(--glass-bg-strong);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 1rem;
    backdrop-filter: blur(20px) saturate(180%);
    box-shadow: var(--glass-shadow);
}

.chat-message {
    margin-bottom: 1rem;
    animation: fadeInUp 0.5s ease-out;
}

.user-message {
    text-align: right;
}

.ai-message {
    text-align: left;
}

.user-message .message-content {
    background: var(--primary-gradient);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 18px 18px 4px 18px;
    display: inline-block;
    max-width: 80%;
    box-shadow: var(--shadow-soft);
}

.ai-message .message-content {
    background: var(--glass-bg-ultra);
    color: var(--text-primary);
    padding: 0.75rem 1rem;
    border-radius: 18px 18px 18px 4px;
    display: inline-block;
    max-width: 85%;
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(15px);
    box-shadow: var(--shadow-soft);
}

.ai-response {
    line-height: 1.6;
}

.ai-response p {
    margin-bottom: 0.75rem;
}

.ai-response ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.ai-response li {
    margin-bottom: 0.25rem;
    color: var(--text-secondary);
}

.ai-response strong {
    color: var(--text-primary);
    font-weight: 600;
}

.loading-dots {
    animation: loadingPulse 1.5s ease-in-out infinite;
}

@keyframes loadingPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Enhanced Dropdown Menu Styling */
.dropdown-menu {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(25px) saturate(180%);
    border: 2px solid rgba(99, 102, 241, 0.2) !important;
    border-radius: 16px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(99, 102, 241, 0.1);
    padding: 0.75rem;
    min-width: 200px;
    margin-top: 0.5rem;
}

.dropdown-item {
    color: var(--text-primary) !important;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(168, 85, 247, 0.1)) !important;
    color: #1e293b !important;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

.dropdown-item:active {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(168, 85, 247, 0.2)) !important;
    color: #0f172a !important;
}

.dropdown-item i {
    color: #6366f1;
    width: 20px;
    margin-right: 0.5rem;
    font-size: 0.9rem;
}

.dropdown-divider {
    border-color: rgba(99, 102, 241, 0.2) !important;
    margin: 0.5rem 0;
    opacity: 0.6;
}

/* 🏆 WINNING FEATURES STYLES */
.feature-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card .feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.feature-card h4 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-weight: 600;
}

.feature-card p {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.demo-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
}

.demo-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    z-index: -1;
}

.demo-output {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    border-left: 4px solid var(--primary-color);
    text-align: left;
}

.demo-result-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.demo-data {
    background: #2d3748;
    color: #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    font-size: 0.85rem;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
}

.comprehensive-demo-results {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.api-category-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    border-left: 4px solid var(--primary-color);
    height: 100%;
}

.api-category-card h6 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.api-category-card code {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
}

.api-doc-content {
    max-height: 70vh;
    overflow-y: auto;
}

/* Winning Features Animations */
@keyframes feature-glow {
    0%, 100% { box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); }
    50% { box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2); }
}

.feature-card:hover {
    animation: feature-glow 2s ease-in-out infinite;
}

/* Trophy Animation */
@keyframes trophy-bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.fa-trophy {
    animation: trophy-bounce 2s ease-in-out infinite;
    color: #ffd700;
}

/* Success Indicators */
.badge.bg-success {
    background: linear-gradient(45deg, #28a745, #20c997) !important;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    border-radius: 20px;
}

/* Demo Button Enhancements */
.btn-gradient {
    background: var(--primary-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-gradient:hover::before {
    left: 100%;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Responsive Design for Winning Features */
@media (max-width: 768px) {
    .feature-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .feature-card .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .demo-section {
        padding: 2rem 1rem;
    }

    .btn-gradient {
        padding: 0.6rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* ============================================================================
   VOICE RECOGNITION STYLES
   ============================================================================ */

#voice-btn {
    transition: all 0.3s ease;
    border-radius: 8px;
}

#voice-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

#voice-btn.btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border-color: #dc2626;
    animation: recording-pulse 1.5s ease-in-out infinite;
}

@keyframes recording-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
}

#recording-indicator {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

#voice-status {
    background: rgba(99, 102, 241, 0.1);
    border-radius: 8px;
    padding: 8px 12px;
    border-left: 3px solid #6366f1;
}

/* Fallback animation for Spline viewer */
.fallback-animation {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(91, 99, 247, 0.1));
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
}

.scanning-effect {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.human-silhouette {
    position: relative;
    z-index: 2;
    animation: pulse 2s ease-in-out infinite;
    font-size: 4rem;
    color: rgba(0, 212, 255, 0.8);
}

.scan-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(0, 212, 255, 0.3) 45%,
        rgba(0, 212, 255, 0.8) 50%,
        rgba(0, 212, 255, 0.3) 55%,
        transparent 100%
    );
    animation: scan 3s ease-in-out infinite;
    z-index: 1;
}

@keyframes scan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ===== INSIGHTS DASHBOARD STYLES ===== */

.insights-dashboard {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    padding-top: 2rem; /* Add top padding for navbar */
}

.insights-hero {
    background: #ffffff;
    padding: 3rem 0;
    color: #2c3e50;
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid #e2e8f0;
}

.insights-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: heroShimmer 8s ease-in-out infinite;
}

@keyframes heroShimmer {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.insights-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2c3e50 !important;
    z-index: 10;
    position: relative;
}

.insights-title[color="white"] {
    color: #2c3e50 !important;
}

.insights-subtitle {
    font-size: 1.3rem;
    opacity: 0.8;
    font-weight: 400;
    color: #64748b !important;
    z-index: 10;
    position: relative;
}

/* Dark text for white background insights dashboard */
#insights-section h1,
#insights-section .insights-title,
#insights-section .insights-subtitle,
.insights-header h1,
.insights-header .insights-title,
.insights-header .insights-subtitle,
.insights-hero h1,
.insights-hero .insights-title,
.insights-hero .insights-subtitle,
.insights-hero p {
    color: #2c3e50 !important;
    text-shadow: none !important;
}

/* Extra specific targeting for the title */
h1.insights-title,
.insights-title[color="white"],
.insights-hero .insights-title {
    color: #2c3e50 !important;
    text-shadow: none !important;
    font-weight: 700;
}

/* Force dark text on white background */
.insights-hero * {
    color: #2c3e50 !important;
}

.insights-hero h1 * {
    color: #2c3e50 !important;
}

/* 🏆 HACKATHON ENHANCED: Interactive Chart Styles */
.cultural-intelligence-popup {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #667eea;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    z-index: 1000;
    max-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.cultural-intelligence-popup .popup-content h6 {
    color: #667eea;
    margin-bottom: 10px;
    font-weight: 600;
}

.cultural-intelligence-popup .popup-content p {
    margin-bottom: 5px;
    font-size: 14px;
    color: #64748b;
}

.real-time-indicator {
    animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-10px); }
    50% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-10px); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Cross-Domain Analysis Modal */
.cross-domain-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cross-domain-modal .modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.cross-domain-modal .modal-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2001;
    animation: modalSlideIn 0.3s ease-out;
}

.cross-domain-modal h5 {
    color: #667eea;
    margin-bottom: 20px;
    text-align: center;
}

.analysis-progress {
    text-align: center;
    padding: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-bar::after {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    animation: progressSlide 2s ease-in-out;
}

@keyframes progressSlide {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes modalSlideIn {
    from { transform: scale(0.8) translateY(-50px); opacity: 0; }
    to { transform: scale(1) translateY(0); opacity: 1; }
}

.analysis-results ul {
    list-style: none;
    padding: 0;
}

.analysis-results li {
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
    color: #64748b;
}

.analysis-results li:last-child {
    border-bottom: none;
}

/* Enhanced Chart Containers */
.chart-container {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    padding: 20px;
    margin-bottom: 30px;
    transition: all 0.3s ease;
}

.chart-container:hover {
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.chart-container .chart-title {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-container .chart-subtitle {
    font-size: 14px;
    color: #64748b;
    margin-bottom: 20px;
}

/* Interactive Chart Enhancements */
.plotly-graph-div {
    border-radius: 8px;
    overflow: hidden;
}

/* Real-time Update Animations */
@keyframes chartPulse {
    0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
    100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
}

.chart-updating {
    animation: chartPulse 1s ease-out;
}

/* 🏆 ENHANCED CASE STUDIES STYLING */
.case-study-card {
    background: var(--glass-bg-ultra) !important;
    backdrop-filter: blur(30px) saturate(200%);
    border: 1px solid var(--glass-border) !important;
    border-radius: 24px !important;
    padding: 2rem;
    height: 100%;
    transition: var(--transition-bounce);
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: var(--glass-shadow);
}

/* Removed colorful border effects */

.case-study-card.featured-case-study {
    background: var(--glass-bg-ultra) !important;
    border: 1px solid var(--glass-border) !important;
    box-shadow: var(--glass-shadow-strong);
    transform: scale(1.02);
}

/* Removed gradient border effects for success/danger states */

.case-study-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-strong);
    background: var(--glass-bg-ultra) !important;
}

/* Removed hover effects for gradient borders */

.case-study-header {
    margin-bottom: 1.5rem;
}

.case-study-company h5 {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 700;
    font-size: 1.4rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.case-study-company small {
    color: var(--text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

.case-study-impact .badge {
    padding: 0.75rem 1.25rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.85rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.case-study-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.case-study-content h6 {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.case-study-content h6 i {
    font-size: 0.9rem;
}

.case-study-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
    margin-bottom: 0;
}

.case-study-footer {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--glass-border);
    text-align: center;
}

.case-study-footer .btn {
    border-radius: 16px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition-bounce);
    backdrop-filter: blur(10px);
}

.integration-badge {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    padding: 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.roi-highlight {
    background: rgba(16, 185, 129, 0.1);
    border-radius: 6px;
    padding: 8px 12px;
    text-align: center;
    margin-top: 10px;
}

.border-danger .roi-highlight {
    background: rgba(239, 68, 68, 0.1);
}

.testimonial-section {
    margin: 15px 0;
    padding: 10px 15px;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 3px solid #667eea;
}

.blockquote-sm {
    font-size: 0.9rem;
    margin-bottom: 0;
}

.alert-sm {
    padding: 8px 12px;
    font-size: 0.85rem;
}

/* Enhanced Case Study Metrics */
.case-study-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.case-study-metric {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 1rem;
    text-align: center;
    transition: var(--transition-smooth);
    backdrop-filter: blur(10px);
}

.case-study-metric:hover {
    background: var(--glass-bg-strong);
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
}

.case-study-metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    font-family: 'Space Grotesk', sans-serif;
}

.case-study-metric-label {
    color: var(--text-muted);
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.case-study-timeline {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    backdrop-filter: blur(15px);
}

.case-study-timeline h6 {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.case-study-testimonial {
    background: var(--glass-bg);
    border-left: 4px solid;
    border-image: var(--primary-gradient) 1;
    border-radius: 0 16px 16px 0;
    padding: 1.5rem;
    margin: 1.5rem 0;
    backdrop-filter: blur(15px);
    position: relative;
}

.case-study-testimonial::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 15px;
    font-size: 3rem;
    color: var(--text-accent);
    font-family: serif;
    opacity: 0.3;
}

.case-study-testimonial p {
    font-style: italic;
    color: var(--text-secondary);
    margin-bottom: 0;
    font-size: 1.05rem;
    line-height: 1.7;
}

.case-study-data-sources {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1rem;
}

.case-study-data-source {
    background: var(--glass-bg-strong);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.case-study-data-source:hover {
    background: var(--glass-bg-ultra);
    transform: translateY(-1px);
    box-shadow: var(--shadow-soft);
}

.case-study-data-source i {
    color: var(--text-accent);
    font-size: 0.8rem;
}

.detailed-case-study h6 {
    font-family: 'Space Grotesk', sans-serif;
    background: var(--indigo-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.detailed-case-study h6:first-child {
    margin-top: 0;
}

.detailed-case-study p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1rem;
}

.detailed-case-study strong {
    color: var(--text-primary);
    font-weight: 600;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e8f0;
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-label {
    font-weight: 600;
    color: #64748b;
}

.metric-value {
    color: #1e293b;
}

/* 🏆 HACKATHON ENHANCED: Cross-Domain Intelligence UI */
.cross-domain-form {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e2e8f0;
}

.cross-domain-results {
    min-height: 400px;
}

.cross-domain-results-content {
    animation: fadeInUp 0.5s ease-out;
}

/* Enhanced metric card labels for cross-domain analysis */
.cross-domain-results .metric-card small {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.5px;
}

/* Premium Case Studies Styling */
.integration-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.integration-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.metric-mini-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.metric-mini-card:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.bg-gradient-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.testimonial-section {
    position: relative;
    overflow: hidden;
}

.testimonial-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.case-study-card .badge {
    font-size: 0.75rem;
    font-weight: 600;
}

.roi-highlight {
    position: relative;
    overflow: hidden;
}

.roi-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.roi-highlight:hover::before {
    left: 100%;
}

/* Enhanced ROI text visibility */
.roi-highlight h5,
.roi-highlight small {
    color: #ffffff !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    font-weight: 700 !important;
}

.roi-highlight {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Case study badge improvements */
.case-study-impact .badge {
    min-width: 120px;
    white-space: nowrap;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    padding: 8px 16px !important;
    font-size: 0.875rem !important;
    border-radius: 20px !important;
}

.case-study-impact {
    flex-shrink: 0;
    margin-left: auto;
}

/* Ensure case study header has proper spacing */
.case-study-header {
    overflow: visible;
}

.case-study-company {
    min-width: 0; /* Allow flex item to shrink */
}

.case-study-card {
    overflow: visible !important;
}

/* Responsive badge adjustments */
@media (max-width: 768px) {
    .case-study-impact .badge {
        min-width: 80px;
        font-size: 0.75rem !important;
        padding: 6px 12px !important;
    }

    .case-study-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .case-study-impact {
        margin-top: 10px;
        margin-left: 0;
    }
}

.cultural-bridge-item {
    background: rgba(102, 126, 234, 0.05);
    border-color: rgba(102, 126, 234, 0.2) !important;
    transition: all 0.3s ease;
}

.cultural-bridge-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.opportunity-item {
    background: rgba(16, 185, 129, 0.05);
    border-color: rgba(16, 185, 129, 0.2) !important;
    transition: all 0.3s ease;
}

.opportunity-item:hover {
    background: rgba(16, 185, 129, 0.1);
    transform: translateY(-2px);
}

.business-metric {
    text-align: center;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.business-app-card {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.business-app-card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.metric-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e8f0;
}

.metric-row:last-child {
    border-bottom: none;
}

.analysis-text {
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-line;
    line-height: 1.6;
}

/* Network Visualization Styles */
.network-visualization-container {
    position: relative;
}

.network-demo-nodes {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.network-node {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 12px;
    text-align: center;
    animation: networkPulse 2s ease-in-out infinite;
}

.network-node.music {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.network-node.fashion {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.network-node.food {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.network-node.travel {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.network-connection {
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    position: relative;
    animation: connectionFlow 3s ease-in-out infinite;
}

.network-connection::after {
    content: '';
    position: absolute;
    right: -5px;
    top: -3px;
    width: 0;
    height: 0;
    border-left: 8px solid #764ba2;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
}

@keyframes networkPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes connectionFlow {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading and Progress Indicators */
.init-progress-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.init-progress-content {
    text-align: center;
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.fade-out {
    opacity: 0;
    transition: opacity 0.5s ease-out;
}

/* Enhanced Metric Cards */
.metric-card {
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.metric-card:hover::before {
    left: 100%;
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Success Message Styling */
.success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    z-index: 9999;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.insights-controls {
    position: relative;
    z-index: 2;
}

.btn-insights-primary {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-insights-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    color: white;
}

.btn-insights-secondary {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-insights-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
}

/* Enhanced Stat Cards */
.insights-stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    border: none;
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 200px;
}

.insights-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.gradient-card-1 {
    border-left: 4px solid #667eea;
}

.gradient-card-2 {
    border-left: 4px solid #f093fb;
}

.gradient-card-3 {
    border-left: 4px solid #10b981;
}

.gradient-card-4 {
    border-left: 4px solid #f59e0b;
}

.stat-icon {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, #667eea, #764ba2);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.stat-content {
    position: relative;
    z-index: 2;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 1rem;
    color: #64748b;
    font-weight: 600;
    margin-bottom: 1rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #10b981;
    font-weight: 600;
}

.stat-trend i {
    font-size: 0.8rem;
}

.stat-sparkline {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
}

/* Enhanced Chart Cards */
.insights-chart-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
    border: none;
    border-radius: 24px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.insights-chart-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
}

.insights-chart-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    animation: shimmer 3s infinite;
}

.chart-header {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(139, 92, 246, 0.05));
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    display: flex;
    align-items: center;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.btn-chart-control {
    background: rgba(102, 126, 234, 0.1);
    border: none;
    color: #667eea;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-chart-control:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(1.1);
    color: #667eea;
}

.chart-body {
    padding: 2rem;
}

.chart-container {
    height: 350px;
    width: 100%;
    position: relative;
}

.chart-container-large {
    height: 400px;
    width: 100%;
    position: relative;
}

.chart-insights {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(148, 163, 184, 0.1);
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.insight-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.insight-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
}

.insight-value {
    font-size: 1rem;
    color: #1e293b;
    font-weight: 700;
}

/* Timeline Card Specific */
.timeline-card {
    border-left: 4px solid #667eea;
}

.timeline-insights {
    display: flex;
    justify-content: space-around;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.timeline-stat {
    text-align: center;
}

.timeline-stat .stat-number {
    font-size: 1.5rem;
    font-weight: 800;
    color: #667eea;
    display: block;
}

.timeline-stat .stat-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
}

/* Taste Card Specific */
.taste-card {
    border-left: 4px solid #f093fb;
}

.taste-insights {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.taste-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    background: rgba(240, 147, 251, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.taste-item:hover {
    background: rgba(240, 147, 251, 0.1);
    transform: translateX(5px);
}

.taste-item.trending {
    border-left: 3px solid #10b981;
}

.taste-icon {
    font-size: 1.25rem;
    margin-right: 0.75rem;
}

.taste-name {
    font-weight: 600;
    color: #1e293b;
    flex: 1;
}

.taste-trend {
    font-weight: 700;
    color: #10b981;
    font-size: 0.875rem;
}

/* Enhanced ROI Calculator Styles */
.enhanced-roi-results .metric-card {
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

/* Ensure text visibility in metric cards */
.enhanced-roi-results .metric-card h2 {
    color: white !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    font-weight: 700 !important;
}

.enhanced-roi-results .metric-card small {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    font-size: 0.875rem !important;
}

/* General metric card text visibility improvements */
.metric-card h2,
.metric-card .h2 {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.metric-card small {
    font-weight: 600;
}

/* ROI Calculator specific improvements */
.business-value-section .metric-card {
    color: white;
}

.business-value-section .metric-card h2,
.business-value-section .metric-card .h2 {
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.business-value-section .metric-card small {
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.enhanced-roi-results .metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.enhanced-roi-results .metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.enhanced-roi-results .metric-card:hover::before {
    left: 100%;
}

.enhanced-roi-results .progress {
    position: relative;
    overflow: visible;
}

.enhanced-roi-results .progress-bar {
    transition: width 1s ease-in-out;
    position: relative;
}

.enhanced-roi-results .card {
    transition: all 0.3s ease;
}

.enhanced-roi-results .card:hover {
    transform: translateY(-2px);
}

.enhanced-roi-results .badge {
    transition: all 0.3s ease;
}

.enhanced-roi-results .badge:hover {
    transform: scale(1.05);
}

/* Case Studies Responsive Design */
@media (max-width: 992px) {
    .case-study-card {
        margin-bottom: 2rem;
    }

    .case-study-metrics {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .case-study-company h5 {
        font-size: 1.2rem;
    }

    .case-study-content h6 {
        font-size: 0.95rem;
    }
}

@media (max-width: 768px) {
    .case-study-card {
        padding: 1.5rem;
        border-radius: 20px !important;
    }

    .case-study-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
    }

    .case-study-impact {
        align-self: flex-end;
    }

    .case-study-metrics {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .case-study-metric {
        padding: 0.75rem;
    }

    .case-study-metric-value {
        font-size: 1.3rem;
    }

    .case-study-data-sources {
        flex-direction: column;
        gap: 0.5rem;
    }

    .case-study-data-source {
        justify-content: center;
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    .case-study-testimonial {
        padding: 1rem;
        margin: 1rem 0;
    }

    .case-study-timeline {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .case-study-card {
        padding: 1.25rem;
        margin-bottom: 1.5rem;
    }

    .case-study-company h5 {
        font-size: 1.1rem;
    }

    .case-study-company small {
        font-size: 0.8rem;
    }

    .case-study-content h6 {
        font-size: 0.9rem;
        gap: 0.25rem;
    }

    .case-study-content p {
        font-size: 0.9rem;
    }

    .case-study-footer .btn {
        width: 100%;
        padding: 0.75rem;
    }
}

/* Market Expansion Opportunities Styling */
.market-opportunity-card {
    background: var(--glass-bg-ultra) !important;
    backdrop-filter: blur(30px) saturate(200%);
    border: 1px solid var(--glass-border) !important;
    border-radius: 20px !important;
    transition: var(--transition-bounce);
    overflow: hidden;
    position: relative;
}

.market-opportunity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: var(--transition-smooth);
}

.market-opportunity-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-strong);
}

.market-opportunity-card:hover::before {
    opacity: 1;
}

.market-opportunity-card .card-header {
    border-radius: 20px 20px 0 0 !important;
    border-bottom: 1px solid var(--glass-border) !important;
    padding: 1.5rem;
}

.market-score .progress {
    background: var(--glass-bg) !important;
    border-radius: 10px !important;
    height: 10px !important;
}

.market-score .progress-bar {
    border-radius: 10px !important;
    transition: width 1s ease-in-out;
}

.market-metrics .metric-value {
    font-size: 1.1rem;
    font-weight: 700;
    font-family: 'Space Grotesk', sans-serif;
}

.expansion-summary {
    border: 1px solid var(--glass-border) !important;
    position: relative;
    overflow: hidden;
}

.expansion-summary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-gradient);
}

.market-expansion-results {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
