#!/bin/bash

# PostgreSQL Installation Script for TasteShift
# This script installs PostgreSQL and required dependencies for Supabase connection

echo "🚀 TasteShift PostgreSQL Installation Script"
echo "============================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for macOS. Please install PostgreSQL manually for your OS."
    exit 1
fi

print_status "Detected macOS system"

# Step 1: Check if Homebrew is installed
print_status "Checking for Homebrew..."
if command -v brew &> /dev/null; then
    print_success "Homebrew is already installed"
    brew --version
else
    print_warning "Homebrew not found. Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # Add Homebrew to PATH for Apple Silicon Macs
    if [[ $(uname -m) == "arm64" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
        eval "$(/opt/homebrew/bin/brew shellenv)"
    fi
    
    if command -v brew &> /dev/null; then
        print_success "Homebrew installed successfully"
    else
        print_error "Failed to install Homebrew. Please install manually."
        exit 1
    fi
fi

# Step 2: Update Homebrew
print_status "Updating Homebrew..."
brew update

# Step 3: Install PostgreSQL
print_status "Installing PostgreSQL..."
if brew list postgresql &> /dev/null; then
    print_success "PostgreSQL is already installed"
else
    brew install postgresql
    if [ $? -eq 0 ]; then
        print_success "PostgreSQL installed successfully"
    else
        print_error "Failed to install PostgreSQL"
        exit 1
    fi
fi

# Step 4: Install libpq (PostgreSQL client libraries)
print_status "Installing libpq (PostgreSQL client libraries)..."
brew install libpq

# Step 5: Add libpq to PATH
print_status "Adding libpq to PATH..."
if [[ $(uname -m) == "arm64" ]]; then
    # Apple Silicon Mac
    export PATH="/opt/homebrew/opt/libpq/bin:$PATH"
    echo 'export PATH="/opt/homebrew/opt/libpq/bin:$PATH"' >> ~/.zprofile
else
    # Intel Mac
    export PATH="/usr/local/opt/libpq/bin:$PATH"
    echo 'export PATH="/usr/local/opt/libpq/bin:$PATH"' >> ~/.bash_profile
fi

# Step 6: Set environment variables for compilation
print_status "Setting environment variables for psycopg2 compilation..."
if [[ $(uname -m) == "arm64" ]]; then
    # Apple Silicon Mac
    export LDFLAGS="-L/opt/homebrew/opt/libpq/lib"
    export CPPFLAGS="-I/opt/homebrew/opt/libpq/include"
else
    # Intel Mac
    export LDFLAGS="-L/usr/local/opt/libpq/lib"
    export CPPFLAGS="-I/usr/local/opt/libpq/include"
fi

# Step 7: Install psycopg2-binary
print_status "Installing psycopg2-binary..."
pip install psycopg2-binary
if [ $? -eq 0 ]; then
    print_success "psycopg2-binary installed successfully"
else
    print_warning "Failed to install psycopg2-binary with pip. Trying alternative methods..."
    
    # Try with specific version
    pip install psycopg2-binary==2.9.7
    if [ $? -eq 0 ]; then
        print_success "psycopg2-binary 2.9.7 installed successfully"
    else
        # Try building from source
        print_status "Trying to build from source..."
        pip install psycopg2
        if [ $? -eq 0 ]; then
            print_success "psycopg2 (source) installed successfully"
        else
            print_error "Failed to install PostgreSQL adapter. Please check the error messages above."
            exit 1
        fi
    fi
fi

# Step 8: Install other requirements
print_status "Installing other Python requirements..."
pip install -r requirements.txt
if [ $? -eq 0 ]; then
    print_success "All requirements installed successfully"
else
    print_warning "Some requirements may have failed to install. Check the output above."
fi

# Step 9: Test PostgreSQL connection
print_status "Testing PostgreSQL installation..."
python -c "import psycopg2; print('✅ psycopg2 import successful')" 2>/dev/null
if [ $? -eq 0 ]; then
    print_success "PostgreSQL adapter is working correctly"
else
    print_error "PostgreSQL adapter test failed"
fi

echo ""
echo "🎉 Installation Complete!"
echo "========================"
print_success "PostgreSQL and dependencies are now installed"
print_success "You can now run: bash start_server.sh"
echo ""
print_status "Next steps:"
echo "  1. Run: bash start_server.sh"
echo "  2. Open: http://localhost:8000"
echo "  3. Your app will connect to Supabase PostgreSQL database"
echo ""
